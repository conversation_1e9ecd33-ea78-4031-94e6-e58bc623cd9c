"""Base agent implementation."""

import abc
import asyncio
import datetime
import json
import logging
import os
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import uuid4

from cli_agent.utils.llm_client import LLMClient
from cli_agent.utils.config import Config


class Agent(abc.ABC):
    """Base Agent class for CLI Agent."""

    def __init__(self, config: Config):
        """Initialize the agent.
        
        Args:
            config: The agent configuration.
        """
        self.config = config
        self.max_steps = config.max_steps
        
        # Initialize LLM client
        self.llm_client = LLMClient(config)
        
        # Task management
        self.task_id = None
        self.task = None
        self.task_args = None
        self.steps_taken = 0
        self.step_results = []
        self.trajectory = []
        self.trajectory_path = None
        self.current_step = None
        
        # Set up logging
        self.logger = logging.getLogger("agent")
        self.logger.setLevel(logging.INFO)
        
        # Initialize console (will be set later)
        self.console = None
        
        # Event store
        self.events = []
    
    def new_task(self, task: str, task_args: Optional[Dict[str, str]] = None) -> None:
        """Set up a new task for the agent to work on.
        
        Args:
            task: Task description.
            task_args: Additional task arguments.
        """
        self.task_id = str(uuid4())
        self.task = task
        self.task_args = task_args or {}
        self.steps_taken = 0
        self.step_results = []
        self.trajectory = []
        self.current_step = None
        
        # Record task start event
        self._record_event("task_start", {
            "task_id": self.task_id,
            "task": self.task,
            "task_args": self.task_args,
        })

    def setup_trajectory_recording(
        self, trajectory_file: Optional[str] = None
    ) -> str:
        """Set up trajectory recording for the task.
        
        Args:
            trajectory_file: Optional path for trajectory file.
            
        Returns:
            Path to the trajectory file.
        """
        if trajectory_file is None:
            # Generate trajectory file path with timestamp and task ID
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            task_id_short = self.task_id[:8] if self.task_id else "no_task"
            filename = f"trajectory_{timestamp}_{task_id_short}.json"
            trajectory_dir = Path("trajectories")
            trajectory_dir.mkdir(exist_ok=True)
            self.trajectory_path = str(trajectory_dir / filename)
        else:
            self.trajectory_path = trajectory_file
        
        return self.trajectory_path
    
    def _record_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Record an event to the agent's event log.
        
        Args:
            event_type: Type of event.
            data: Event data.
        """
        timestamp = time.time()
        event = {
            "timestamp": timestamp,
            "datetime": datetime.datetime.fromtimestamp(timestamp).isoformat(),
            "event_type": event_type,
            "data": data,
        }
        self.events.append(event)
    
    def _save_trajectory(self) -> None:
        """Save trajectory data to file."""
        if not self.trajectory_path:
            return
        
        try:
            trajectory_data = {
                "task_id": self.task_id,
                "task": self.task,
                "task_args": self.task_args,
                "steps": self.trajectory,
                "events": self.events,
            }
            
            # Ensure directory exists
            trajectory_dir = os.path.dirname(self.trajectory_path)
            if trajectory_dir and not os.path.exists(trajectory_dir):
                os.makedirs(trajectory_dir)
                
            with open(self.trajectory_path, "w", encoding="utf-8") as f:
                json.dump(trajectory_data, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            if self.console:
                self.console.print(f"[red]Error saving trajectory: {e}[/red]")
            self.logger.error(f"Error saving trajectory: {e}")
            self.logger.error(traceback.format_exc())
    
    def set_cli_console(self, console):
        """Set the CLI console for output.
        
        Args:
            console: CLI console for output.
        """
        self.console = console
    
    @abc.abstractmethod
    async def execute_step(self, step_idx: int) -> Tuple[bool, Dict[str, Any]]:
        """Execute a single agent step.
        
        Args:
            step_idx: Step index.
            
        Returns:
            Tuple of (is_done, step_result)
        """
        pass
    
    @abc.abstractmethod
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the full task.
        
        Returns:
            Task results.
        """
        pass
