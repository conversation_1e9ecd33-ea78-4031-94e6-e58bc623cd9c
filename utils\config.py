"""Configuration management for CLI Agent."""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union


class ModelConfig:
    """Configuration for an LLM model provider."""
    
    def __init__(self, **kwargs):
        """Initialize model configuration.
        
        Args:
            **kwargs: Configuration parameters.
        """
        self.model = kwargs.get("model")
        self.api_key = kwargs.get("api_key")
        self.max_tokens = kwargs.get("max_tokens", 4096)
        self.temperature = kwargs.get("temperature", 0.7)
        self.top_p = kwargs.get("top_p", 1.0)
        self.top_k = kwargs.get("top_k")
        self.frequency_penalty = kwargs.get("frequency_penalty", 0.0)
        self.presence_penalty = kwargs.get("presence_penalty", 0.0)


class Config:
    """Configuration manager for CLI Agent."""
    
    def __init__(self, config_file: str = "cli_config.json"):
        """Initialize configuration from a file.
        
        Args:
            config_file: Path to configuration file.
        """
        self.config_file = config_file
        
        # Default configuration
        self.default_provider = "openai"
        self.max_steps = 20
        self.memory_db_path = "memory.db"
        self.working_dir = os.getcwd()
        self.model_providers = {
            "openai": ModelConfig(
                model="gpt-3.5-turbo",
                max_tokens=4096,
                temperature=0.7,
            ),
            "anthropic": ModelConfig(
                model="claude-2",
                max_tokens=8192,
                temperature=0.7,
                top_k=10,
            ),
            "openrouter": ModelConfig(
                model="openai/gpt-3.5-turbo",
                max_tokens=4096,
                temperature=0.7,
            ),
            "azure": ModelConfig(
                model="gpt-35-turbo",
                max_tokens=4096,
                temperature=0.7,
            ),
            "google": ModelConfig(
                model="gemini-pro",
                max_tokens=2048,
                temperature=0.7,
            ),
        }
        
        # Load configuration from file if it exists
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        try:
            config_path = Path(self.config_file)
            
            if not config_path.exists():
                # Try looking in parent directories
                for parent in config_path.parents:
                    candidate = parent / self.config_file
                    if candidate.exists():
                        config_path = candidate
                        break
            
            if config_path.exists():
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
                
                # Update configuration
                self._update_from_dict(config_data)
        
        except Exception as e:
            print(f"Warning: Error loading configuration from {self.config_file}: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary.
        """
        # Update general settings
        if "default_provider" in config_data:
            self.default_provider = config_data["default_provider"]
        
        if "max_steps" in config_data:
            self.max_steps = config_data["max_steps"]
        
        if "memory_db_path" in config_data:
            self.memory_db_path = config_data["memory_db_path"]
        
        if "working_dir" in config_data:
            self.working_dir = config_data["working_dir"]
        
        # Update model providers
        if "model_providers" in config_data:
            for provider, provider_config in config_data["model_providers"].items():
                if provider in self.model_providers:
                    # Update existing provider
                    for key, value in provider_config.items():
                        setattr(self.model_providers[provider], key, value)
                else:
                    # Add new provider
                    self.model_providers[provider] = ModelConfig(**provider_config)
    
    def save(self, path: Optional[str] = None) -> None:
        """Save configuration to file.
        
        Args:
            path: Optional path to save to. Defaults to config_file.
        """
        save_path = path or self.config_file
        
        try:
            # Convert to dictionary
            config_data = {
                "default_provider": self.default_provider,
                "max_steps": self.max_steps,
                "memory_db_path": self.memory_db_path,
                "working_dir": self.working_dir,
                "model_providers": {},
            }
            
            # Add model providers
            for provider, provider_config in self.model_providers.items():
                config_data["model_providers"][provider] = {
                    key: value
                    for key, value in provider_config.__dict__.items()
                    if value is not None
                }
            
            # Save to file
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2)
        
        except Exception as e:
            print(f"Error saving configuration to {save_path}: {e}")


def resolve_config_value(
    cli_value: Any, config_value: Any, env_var: Optional[str] = None
) -> Any:
    """Resolve configuration value precedence.
    
    Precedence: CLI > Environment > Config File
    
    Args:
        cli_value: Value from CLI arguments.
        config_value: Value from configuration file.
        env_var: Optional environment variable name.
        
    Returns:
        Resolved value.
    """
    # CLI arguments have highest precedence
    if cli_value is not None:
        return cli_value
    
    # Environment variables have second precedence
    if env_var and env_var in os.environ:
        return os.environ[env_var]
    
    # Config file has lowest precedence
    return config_value
