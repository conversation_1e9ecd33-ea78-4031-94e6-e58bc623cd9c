"""Tools module for CLI Agent."""

import os
from typing import Dict, Type

from .base import Tool, ToolExecutor
from .file_tools import ReadFileTool, WriteFileTool, ListFilesTool
from .shell_tools import RunShell<PERSON>ommandTool
from .git_tools import GetGitInfoTool
from .search_tools import SearchCodeTool

# Tool registry - Maps tool names to tool classes
tools_registry = {
    "read_file": ReadFileTool,
    "write_file": WriteFileTool,
    "list_files": ListFilesTool,
    "run_shell": RunShellCommandTool,
    "git_info": GetGitInfoTool,
    "search_code": SearchCodeTool,
}


def get_default_tools() -> Dict[str, Type[Tool]]:
    """Get the default set of tools.
    
    Returns:
        Dictionary mapping tool names to tool classes.
    """
    return tools_registry
