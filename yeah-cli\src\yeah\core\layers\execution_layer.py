"""
Execution Layer - Translates validated intent and knowledge into real actions
Part of Kiro's Four-Layer Agentic Architecture
"""

import os
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from datetime import datetime

class ActionType(Enum):
    """Types of actions the execution layer can perform"""
    CREATE_FILE = "create_file"
    MODIFY_FILE = "modify_file"
    DELETE_FILE = "delete_file"
    RUN_COMMAND = "run_command"
    INSTALL_DEPENDENCY = "install_dependency"
    CREATE_DIRECTORY = "create_directory"
    GENERATE_CODE = "generate_code"
    UPDATE_DOCUMENTATION = "update_documentation"

class ExecutionResult:
    """Result of an execution action"""
    
    def __init__(self):
        self.success: bool = False
        self.action_type: ActionType = ActionType.CREATE_FILE
        self.details: Dict[str, Any] = {}
        self.output: str = ""
        self.error: Optional[str] = None
        self.timestamp: datetime = datetime.now()
        self.files_affected: List[str] = []

class ExecutionPlan:
    """Plan for executing a series of actions"""
    
    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.dependencies: List[str] = []
        self.estimated_duration: int = 0  # in minutes
        self.risk_level: str = "low"  # low, medium, high
        self.rollback_plan: List[Dict[str, Any]] = []

class ExecutionLayer:
    """
    Execution Layer implementation following Kiro's principles:
    - Translates validated intent and knowledge into real actions
    - Supports both autonomous (Autopilot) and supervised modes
    - Provides hooks for event-driven automation
    - Maintains audit trail of all actions
    """
    
    def __init__(self):
        self.hooks: Dict[str, List[Callable]] = {
            'before_file_create': [],
            'after_file_create': [],
            'before_file_modify': [],
            'after_file_modify': [],
            'before_command_run': [],
            'after_command_run': [],
            'before_dependency_install': [],
            'after_dependency_install': []
        }
        self.action_history: List[ExecutionResult] = []
        self.dry_run_mode: bool = False
    
    def create_execution_plan(self, intent, knowledge) -> ExecutionPlan:
        """
        Create a detailed execution plan based on intent and knowledge
        """
        plan = ExecutionPlan()
        
        # Determine actions based on intent category
        if intent.category.value == "plan_product":
            plan.actions = self._plan_product_actions(intent, knowledge)
        elif intent.category.value == "create_spec":
            plan.actions = self._plan_spec_creation_actions(intent, knowledge)
        elif intent.category.value == "execute_tasks":
            plan.actions = self._plan_task_execution_actions(intent, knowledge)
        elif intent.category.value == "analyze_product":
            plan.actions = self._plan_analysis_actions(intent, knowledge)
        else:
            plan.actions = self._plan_general_actions(intent, knowledge)
        
        # Estimate duration and risk
        plan.estimated_duration = self._estimate_duration(plan.actions)
        plan.risk_level = self._assess_risk_level(plan.actions)
        
        # Create rollback plan
        plan.rollback_plan = self._create_rollback_plan(plan.actions)
        
        return plan
    
    def execute_plan(self, plan: ExecutionPlan, approval_callback: Optional[Callable] = None) -> List[ExecutionResult]:
        """
        Execute the planned actions with optional approval callback
        """
        results = []
        
        for action in plan.actions:
            # Request approval if callback provided
            if approval_callback and not approval_callback(action):
                result = ExecutionResult()
                result.success = False
                result.error = "Action not approved by user"
                result.action_type = ActionType(action['type'])
                results.append(result)
                continue
            
            # Execute the action
            result = self._execute_action(action)
            results.append(result)
            self.action_history.append(result)
            
            # Stop on failure if critical action
            if not result.success and action.get('critical', False):
                break
        
        return results
    
    def register_hook(self, event: str, callback: Callable):
        """Register a hook for event-driven automation"""
        if event in self.hooks:
            self.hooks[event].append(callback)
    
    def trigger_hooks(self, event: str, context: Dict[str, Any]):
        """Trigger all hooks for a specific event"""
        for callback in self.hooks.get(event, []):
            try:
                callback(context)
            except Exception as e:
                print(f"Hook execution failed for {event}: {e}")
    
    def _plan_product_actions(self, intent, knowledge) -> List[Dict[str, Any]]:
        """Plan actions for product initialization"""
        actions = []
        
        # Create .agent-os directory structure
        actions.append({
            'type': ActionType.CREATE_DIRECTORY.value,
            'path': '.agent-os/product',
            'description': 'Create Agent OS product directory'
        })
        
        actions.append({
            'type': ActionType.CREATE_DIRECTORY.value,
            'path': '.agent-os/specs',
            'description': 'Create Agent OS specs directory'
        })
        
        # Create core product files
        product_files = ['mission.md', 'tech-stack.md', 'roadmap.md', 'decisions.md']
        for file_name in product_files:
            actions.append({
                'type': ActionType.CREATE_FILE.value,
                'path': f'.agent-os/product/{file_name}',
                'content': self._get_template_content(file_name, intent.parameters),
                'description': f'Create {file_name}'
            })
        
        return actions
    
    def _plan_spec_creation_actions(self, intent, knowledge) -> List[Dict[str, Any]]:
        """Plan actions for spec creation"""
        actions = []
        
        feature_name = intent.parameters.get('feature_name', 'new-feature')
        spec_dir = f'.agent-os/specs/{feature_name}'
        
        # Create spec directory
        actions.append({
            'type': ActionType.CREATE_DIRECTORY.value,
            'path': spec_dir,
            'description': f'Create spec directory for {feature_name}'
        })
        
        # Create spec files from structured docs
        for doc_name, content in intent.structured_docs.items():
            actions.append({
                'type': ActionType.CREATE_FILE.value,
                'path': f'{spec_dir}/{doc_name}',
                'content': content,
                'description': f'Create {doc_name} for {feature_name}'
            })
        
        return actions
    
    def _plan_task_execution_actions(self, intent, knowledge) -> List[Dict[str, Any]]:
        """Plan actions for task execution"""
        actions = []
        
        # Find active spec and tasks
        active_specs = knowledge.codebase_state.get('active_specs', [])
        if not active_specs:
            actions.append({
                'type': ActionType.CREATE_FILE.value,
                'path': 'execution_error.md',
                'content': 'No active specs found. Please create a spec first.',
                'description': 'Report missing spec error'
            })
            return actions
        
        # For now, create a placeholder implementation plan
        actions.append({
            'type': ActionType.GENERATE_CODE.value,
            'spec': active_specs[0],
            'description': 'Generate code based on active spec'
        })
        
        return actions
    
    def _plan_analysis_actions(self, intent, knowledge) -> List[Dict[str, Any]]:
        """Plan actions for product analysis"""
        actions = []
        
        # Analyze codebase structure
        actions.append({
            'type': ActionType.CREATE_FILE.value,
            'path': 'analysis_report.md',
            'content': self._generate_analysis_report(knowledge),
            'description': 'Generate codebase analysis report'
        })
        
        return actions
    
    def _plan_general_actions(self, intent, knowledge) -> List[Dict[str, Any]]:
        """Plan actions for general requests"""
        actions = []
        
        # Create a response file with the analysis
        actions.append({
            'type': ActionType.CREATE_FILE.value,
            'path': 'response.md',
            'content': f"# Response to: {intent.raw_input}\n\nProcessed as general query.",
            'description': 'Create response file'
        })
        
        return actions
    
    def _execute_action(self, action: Dict[str, Any]) -> ExecutionResult:
        """Execute a single action"""
        result = ExecutionResult()
        result.action_type = ActionType(action['type'])
        
        try:
            if self.dry_run_mode:
                result.success = True
                result.output = f"DRY RUN: Would execute {action['type']}"
                return result
            
            # Trigger before hooks
            self.trigger_hooks(f"before_{action['type']}", action)
            
            if action['type'] == ActionType.CREATE_FILE.value:
                result = self._create_file(action)
            elif action['type'] == ActionType.MODIFY_FILE.value:
                result = self._modify_file(action)
            elif action['type'] == ActionType.CREATE_DIRECTORY.value:
                result = self._create_directory(action)
            elif action['type'] == ActionType.RUN_COMMAND.value:
                result = self._run_command(action)
            elif action['type'] == ActionType.GENERATE_CODE.value:
                result = self._generate_code(action)
            else:
                result.success = False
                result.error = f"Unknown action type: {action['type']}"
            
            # Trigger after hooks
            self.trigger_hooks(f"after_{action['type']}", {**action, 'result': result})
            
        except Exception as e:
            result.success = False
            result.error = str(e)
        
        return result
    
    def _create_file(self, action: Dict[str, Any]) -> ExecutionResult:
        """Create a file"""
        result = ExecutionResult()
        result.action_type = ActionType.CREATE_FILE
        
        file_path = Path(action['path'])
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_path.write_text(action['content'], encoding='utf-8')
        
        result.success = True
        result.files_affected = [str(file_path)]
        result.output = f"Created file: {file_path}"
        
        return result
    
    def _create_directory(self, action: Dict[str, Any]) -> ExecutionResult:
        """Create a directory"""
        result = ExecutionResult()
        result.action_type = ActionType.CREATE_DIRECTORY
        
        dir_path = Path(action['path'])
        dir_path.mkdir(parents=True, exist_ok=True)
        
        result.success = True
        result.output = f"Created directory: {dir_path}"
        
        return result
    
    def _modify_file(self, action: Dict[str, Any]) -> ExecutionResult:
        """Modify an existing file"""
        result = ExecutionResult()
        result.action_type = ActionType.MODIFY_FILE
        
        # Implementation would depend on modification type
        result.success = True
        result.output = "File modification not yet implemented"
        
        return result
    
    def _run_command(self, action: Dict[str, Any]) -> ExecutionResult:
        """Run a shell command"""
        result = ExecutionResult()
        result.action_type = ActionType.RUN_COMMAND
        
        try:
            process_result = subprocess.run(
                action['command'],
                shell=True,
                capture_output=True,
                text=True,
                timeout=action.get('timeout', 30)
            )
            
            result.success = process_result.returncode == 0
            result.output = process_result.stdout
            if not result.success:
                result.error = process_result.stderr
                
        except subprocess.TimeoutExpired:
            result.success = False
            result.error = "Command timed out"
        
        return result
    
    def _generate_code(self, action: Dict[str, Any]) -> ExecutionResult:
        """Generate code based on spec"""
        result = ExecutionResult()
        result.action_type = ActionType.GENERATE_CODE
        
        # Placeholder for code generation logic
        result.success = True
        result.output = "Code generation not yet implemented"
        
        return result
    
    def _get_template_content(self, file_name: str, parameters: Dict[str, Any]) -> str:
        """Get template content for a file"""
        templates = {
            'mission.md': """# Product Mission

## Overview
[Describe the product's purpose and goals]

## Target Users
[Define who will use this product]

## Key Features
[List the main features and capabilities]
""",
            'tech-stack.md': """# Technical Stack

## Core Technologies
- **Language:** [Primary programming language]
- **Framework:** [Main framework]
- **Database:** [Database technology]

## Development Tools
- **Testing:** [Testing framework]
- **Build:** [Build tools]
- **Deployment:** [Deployment strategy]
""",
            'roadmap.md': """# Development Roadmap

## Phase 1: Foundation
- [ ] Core setup
- [ ] Basic functionality

## Phase 2: Features
- [ ] Feature 1
- [ ] Feature 2

## Phase 3: Polish
- [ ] Testing
- [ ] Documentation
""",
            'decisions.md': """# Architectural Decisions

## Decision Log

### [Date]: [Decision Title]
**Status:** Proposed/Accepted/Rejected
**Context:** [Why this decision was needed]
**Decision:** [What was decided]
**Consequences:** [Impact of this decision]
"""
        }
        
        return templates.get(file_name, f"# {file_name}\n\n[Content for {file_name}]")
    
    def _generate_analysis_report(self, knowledge) -> str:
        """Generate an analysis report of the codebase"""
        report = "# Codebase Analysis Report\n\n"
        
        if knowledge.product_context:
            report += "## Product Context\n"
            for key, value in knowledge.product_context.items():
                report += f"- **{key.title()}:** Found\n"
        
        if knowledge.codebase_state.get('structure'):
            file_count = len([f for f in knowledge.codebase_state['structure'].values() if f['type'] == 'file'])
            report += f"\n## Codebase Structure\n- **Files:** {file_count}\n"
        
        return report
    
    def _estimate_duration(self, actions: List[Dict[str, Any]]) -> int:
        """Estimate duration for actions in minutes"""
        base_times = {
            ActionType.CREATE_FILE.value: 1,
            ActionType.MODIFY_FILE.value: 2,
            ActionType.CREATE_DIRECTORY.value: 1,
            ActionType.RUN_COMMAND.value: 5,
            ActionType.GENERATE_CODE.value: 15
        }
        
        total = sum(base_times.get(action['type'], 5) for action in actions)
        return total
    
    def _assess_risk_level(self, actions: List[Dict[str, Any]]) -> str:
        """Assess risk level of the planned actions"""
        high_risk_actions = [ActionType.DELETE_FILE.value, ActionType.RUN_COMMAND.value]
        
        if any(action['type'] in high_risk_actions for action in actions):
            return "high"
        elif len(actions) > 10:
            return "medium"
        else:
            return "low"
    
    def _create_rollback_plan(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create a rollback plan for the actions"""
        rollback = []
        
        for action in reversed(actions):
            if action['type'] == ActionType.CREATE_FILE.value:
                rollback.append({
                    'type': ActionType.DELETE_FILE.value,
                    'path': action['path']
                })
            elif action['type'] == ActionType.CREATE_DIRECTORY.value:
                rollback.append({
                    'type': ActionType.DELETE_FILE.value,
                    'path': action['path']
                })
        
        return rollback
