# Product Mission

> Last Updated: 2024-12-19
> Version: 1.0.0

## Pitch

Yeah <PERSON><PERSON><PERSON> is the first coding agent built natively on Agent OS principles. While other AI coding tools operate as general-purpose assistants, Yeah CLI is designed specifically to enforce structured, spec-driven development workflows that ensure consistent, high-quality code output.

## Users

### Primary Users
- **Individual Developers** using AI coding assistants who want more structure and consistency
- **Development Teams** adopting Agent OS methodology for collaborative projects
- **Technical Leaders** seeking to standardize AI-assisted development practices

### Use Cases
- Initialize new projects with complete Agent OS documentation
- Plan features through structured spec creation
- Execute development tasks while maintaining context and standards
- Analyze existing codebases and retrofit them with Agent OS structure

## The Problem

Current AI coding tools suffer from context loss, inconsistent output, and lack of structured workflows. Developers often get:
- Code that doesn't align with project standards
- Features built without proper planning or documentation
- Inconsistent patterns across different development sessions
- No systematic way to maintain project context and decisions

## Differentiators

### Context-First Architecture
Unlike general-purpose AI tools, Yeah CLI always loads and maintains full project context before any operation.

### Agent OS Native
Built specifically for Agent OS workflows - not a general tool with Agent OS features bolted on.

### Standards Enforcement
Automatically applies and validates against established coding standards and best practices.

### Workflow Discipline
Enforces proper development workflows - no ad-hoc coding without specs and planning.

## Key Features

### Core Features
- **Agent OS Workflow Engine** - Native implementation of all four core Agent OS workflows
- **Context Management System** - Loads and maintains project mission, roadmap, standards, and current state
- **Markdown Interpreter** - Reads Agent OS .md files as executable specifications
- **Standards Validation** - Ensures all generated code meets established standards

### Intelligence Features
- **Project Phase Awareness** - Understands current development phase and priorities
- **Decision Tracking** - Maintains history of architectural and product decisions
- **Spec Compliance** - Only executes tasks that align with documented specifications
- **Cross-Session Memory** - Remembers project context across CLI sessions

### Developer Experience Features
- **Command Simplicity** - Clean `/plan-product`, `/create-spec`, `/execute-tasks`, `/analyze-product` interface
- **Validation Feedback** - Clear feedback when operations don't align with Agent OS principles
- **Progress Tracking** - Visual indication of roadmap progress and current tasks
- **Multi-LLM Support** - Works with OpenAI, Anthropic, Google AI, and other providers