"""
Core Agent OS Engine - Interprets and executes Agent OS workflows
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
from .markdown_processor import MarkdownProcessor
from .context_loader import ContextLoader
from .standards_manager import StandardsManager
from .workflow_executor import WorkflowExecutor

class AgentOSEngine:
    """Core engine that interprets and executes Agent OS workflows"""
    
    def __init__(self):
        self.home_path = Path.home()
        self.agent_os_path = self.home_path / ".agent-os"
        self.project_agent_os_path = Path.cwd() / ".agent-os"
        
        self.markdown_processor = MarkdownProcessor()
        self.context_loader = ContextLoader()
        self.standards_manager = StandardsManager()
        self.workflow_executor = WorkflowExecutor()
    
    def load_instruction(self, instruction_name: str) -> Dict:
        """Load Agent OS instruction file and parse it"""
        instruction_path = self.agent_os_path / "instructions" / f"{instruction_name}.md"
        
        if not instruction_path.exists():
            raise FileNotFoundError(f"Agent OS instruction not found: {instruction_path}")
        
        return self.markdown_processor.parse_instruction_file(instruction_path)
    
    def load_project_context(self) -> Dict:
        """Load full Agent OS project context"""
        return self.context_loader.load_full_context(self.project_agent_os_path)
    
    def load_standards(self) -> Dict:
        """Load Agent OS standards with project overrides"""
        return self.standards_manager.load_all_standards(
            global_path=self.agent_os_path / "standards",
            project_path=self.project_agent_os_path / "product"
        )
    
    def execute_workflow(self, instruction_name: str, user_input: Optional[Dict] = None) -> bool:
        """Execute complete Agent OS workflow"""
        try:
            # Load instruction
            instruction = self.load_instruction(instruction_name)
            
            # Load context and standards
            context = self.load_project_context()
            standards = self.load_standards()
            
            # Execute workflow
            return self.workflow_executor.execute(
                instruction=instruction,
                context=context,
                standards=standards,
                user_input=user_input or {}
            )
            
        except Exception as e:
            print(f"Error executing workflow {instruction_name}: {e}")
            return False
    
    def validate_agent_os_structure(self) -> bool:
        """Validate that current project has proper Agent OS structure"""
        required_files = [
            ".agent-os/product/mission.md",
            ".agent-os/product/tech-stack.md",
            ".agent-os/product/roadmap.md"
        ]
        
        for file_path in required_files:
            if not (Path.cwd() / file_path).exists():
                return False
        
        return True