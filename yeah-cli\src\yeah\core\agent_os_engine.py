"""
Core Agent OS Engine - Implements <PERSON><PERSON>'s Four-Layer Agentic Architecture
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from .markdown_processor import MarkdownProcessor
from .layers.intent_layer import IntentLayer
from .layers.knowledge_layer import KnowledgeLayer
from .layers.execution_layer import ExecutionLayer
from .layers.oversight_layer import OversightLayer

class OperationMode(Enum):
    """Operation modes for the Agent OS Engine"""
    SUPERVISED = "supervised"  # Requires user approval for each action
    AUTOPILOT = "autopilot"   # Autonomous operation with oversight

class AgentOSEngine:
    """
    Core engine implementing Kiro's Four-Layer Agentic Architecture:
    - Intent Layer: Captures and parses user input
    - Knowledge Layer: Enriches intent with contextual understanding
    - Execution Layer: Translates intent into real actions
    - Oversight Layer: Monitors and ensures adherence to requirements
    """

    def __init__(self, mode: OperationMode = OperationMode.SUPERVISED):
        self.home_path = Path.home()
        self.agent_os_path = self.home_path / ".agent-os"
        self.project_agent_os_path = Path.cwd() / ".agent-os"
        self.mode = mode

        # Initialize the four layers
        self.intent_layer = IntentLayer()
        self.knowledge_layer = KnowledgeLayer(
            global_path=self.agent_os_path,
            project_path=self.project_agent_os_path
        )
        self.execution_layer = ExecutionLayer()
        self.oversight_layer = OversightLayer(mode=mode)

        # Legacy components for backward compatibility
        self.markdown_processor = MarkdownProcessor()
    
    def process_request(self, user_input: str, approval_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Process a user request through Kiro's Four-Layer Architecture

        Returns:
            Dict containing the processing results and any outputs
        """
        try:
            # Layer 1: Intent - Parse and understand user input
            intent = self.intent_layer.parse_intent(user_input)

            # Layer 2: Knowledge - Enrich with contextual understanding
            knowledge = self.knowledge_layer.enrich_intent(intent, intent.context_requirements)

            # Layer 3: Execution - Create execution plan
            execution_plan = self.execution_layer.create_execution_plan(intent, knowledge)

            # Layer 4: Oversight - Review and validate plan
            oversight_report = self.oversight_layer.review_execution_plan(execution_plan, intent, knowledge)

            # Determine if approval is needed
            needs_approval = self.oversight_layer.requires_approval(oversight_report)

            result = {
                'intent': intent,
                'knowledge_context': knowledge,
                'execution_plan': execution_plan,
                'oversight_report': oversight_report,
                'needs_approval': needs_approval,
                'executed': False,
                'execution_results': []
            }

            # Execute if approved or in autopilot mode
            if not needs_approval or (approval_callback and approval_callback(execution_plan, oversight_report)):
                execution_results = self.execution_layer.execute_plan(execution_plan, approval_callback)
                result['executed'] = True
                result['execution_results'] = execution_results

            return result

        except Exception as e:
            return {
                'error': str(e),
                'executed': False,
                'needs_approval': False
            }

    # Legacy methods for backward compatibility
    def load_instruction(self, instruction_name: str) -> Dict:
        """Load Agent OS instruction file and parse it (legacy method)"""
        instruction_path = self.agent_os_path / "instructions" / f"{instruction_name}.md"

        if not instruction_path.exists():
            raise FileNotFoundError(f"Agent OS instruction not found: {instruction_path}")

        return self.markdown_processor.parse_instruction_file(instruction_path)

    def execute_workflow(self, instruction_name: str, user_input: Optional[Dict] = None) -> bool:
        """Execute complete Agent OS workflow (legacy method)"""
        try:
            # Convert to new four-layer approach
            if instruction_name == "plan-product":
                command = "/plan-product"
            elif instruction_name == "create-spec":
                command = "/create-spec"
            elif instruction_name == "execute-tasks":
                command = "/execute-tasks"
            elif instruction_name == "analyze-product":
                command = "/analyze-product"
            else:
                command = instruction_name

            result = self.process_request(command)
            return result.get('executed', False)

        except Exception as e:
            print(f"Error executing workflow {instruction_name}: {e}")
            return False
    
    def validate_agent_os_structure(self) -> bool:
        """Validate that current project has proper Agent OS structure"""
        required_files = [
            ".agent-os/product/mission.md",
            ".agent-os/product/tech-stack.md",
            ".agent-os/product/roadmap.md"
        ]

        for file_path in required_files:
            if not (Path.cwd() / file_path).exists():
                return False

        return True

    def set_mode(self, mode: OperationMode):
        """Set the operation mode (supervised or autopilot)"""
        self.mode = mode
        self.oversight_layer.mode = mode

    def register_hook(self, event: str, callback: Callable):
        """Register a hook for event-driven automation"""
        self.execution_layer.register_hook(event, callback)

    def get_oversight_history(self) -> List:
        """Get the history of oversight reports"""
        return self.oversight_layer.oversight_history

    def enable_dry_run(self, enabled: bool = True):
        """Enable or disable dry run mode"""
        self.execution_layer.dry_run_mode = enabled