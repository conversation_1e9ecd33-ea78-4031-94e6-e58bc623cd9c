"""LLM client for CLI Agent."""

import enum
import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

import aiohttp
from dotenv import load_dotenv

from .config import Config, ModelConfig

# Load environment variables
_ = load_dotenv()

logger = logging.getLogger(__name__)


class Provider(enum.Enum):
    """Supported LLM providers."""
    
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OPENROUTER = "openrouter"
    AZURE = "azure"
    GOOGLE = "google"


class LLMClient:
    """Client for interacting with LLM providers."""
    
    def __init__(self, config: Config):
        """Initialize LLM client.
        
        Args:
            config: Agent configuration.
        """
        self.config = config
        
        # Initialize provider
        provider_name = config.default_provider or "openai"
        try:
            self.provider = Provider(provider_name)
        except ValueError:
            logger.warning(f"Unknown provider {provider_name}, defaulting to OpenAI")
            self.provider = Provider.OPENAI
        
        # Get model configuration
        self.model_config = config.model_providers.get(
            self.provider.value, ModelConfig()
        )
    
    def set_provider(self, provider: str) -> None:
        """Set the active LLM provider.
        
        Args:
            provider: Provider name.
        """
        try:
            self.provider = Provider(provider)
            self.model_config = self.config.model_providers.get(
                self.provider.value, ModelConfig()
            )
            logger.info(f"Provider set to {provider}")
        except ValueError:
            logger.error(f"Invalid provider {provider}")
            raise ValueError(f"Invalid provider: {provider}")
    
    def format_prompt(self, data: Dict[str, Any]) -> str:
        """Format prompt data based on the active provider.
        
        Args:
            data: Prompt data.
            
        Returns:
            Formatted prompt string.
        """
        # Default format is just stringified JSON
        task = data.get("task", "")
        step = data.get("step", 0)
        previous_steps = data.get("previous_steps", [])
        context = data.get("context", {})
        
        if self.provider == Provider.ANTHROPIC:
            # Format prompt for Claude
            prompt = f"""<task>
{task}
</task>

<step>{step}</step>

"""
            
            # Add context
            if context:
                prompt += "<context>\n"
                for key, value in context.items():
                    if isinstance(value, dict) and key != "memories":
                        prompt += f"<{key}>\n{json.dumps(value, indent=2)}\n</{key}>\n\n"
                    elif isinstance(value, list):
                        prompt += f"<{key}>\n"
                        for item in value:
                            prompt += f"- {item}\n"
                        prompt += f"</{key}>\n\n"
                    else:
                        prompt += f"<{key}>\n{value}\n</{key}>\n\n"
                prompt += "</context>\n\n"
            
            # Add memories if available
            if context.get("memories"):
                prompt += "<memories>\n"
                for memory in context["memories"]:
                    prompt += f"<memory>\n{memory}\n</memory>\n\n"
                prompt += "</memories>\n\n"
            
            # Add previous steps
            if previous_steps:
                prompt += "<previous_steps>\n"
                for i, step_data in enumerate(previous_steps):
                    prompt += f"<step_{i}>\n"
                    if "reasoning" in step_data:
                        prompt += f"<reasoning>{step_data['reasoning']}</reasoning>\n"
                    if "action" in step_data and step_data["action"]:
                        prompt += f"<action>{step_data['action']}</action>\n"
                        if "action_input" in step_data:
                            prompt += f"<action_input>{json.dumps(step_data['action_input'], indent=2)}</action_input>\n"
                        if "action_output" in step_data:
                            prompt += f"<action_output>{json.dumps(step_data['action_output'], indent=2)}</action_output>\n"
                    prompt += f"</step_{i}>\n\n"
                prompt += "</previous_steps>\n\n"
            
            # Add instructions
            prompt += """I'm an AI assistant for software engineering tasks. I'll help you solve the task step by step.

For this step, I need to:
1. Analyze the current task state and context
2. Provide reasoning about what to do next
3. Decide if an action is needed and what it should be

If I need to take an action, I'll provide it in the following JSON format:

```action
{
  "name": "tool_name",
  "input": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

If the task is complete, I'll clearly state "Task complete".

I'll start by analyzing the task and context:

"""
        
        elif self.provider == Provider.OPENAI or self.provider == Provider.AZURE or self.provider == Provider.OPENROUTER:
            # Format prompt for OpenAI, Azure, or OpenRouter
            prompt = f"""# Task
{task}

# Current Step
{step}

"""
            
            # Add context
            if context:
                prompt += "# Context\n"
                for key, value in context.items():
                    if isinstance(value, dict) and key != "memories":
                        prompt += f"## {key.title()}\n```json\n{json.dumps(value, indent=2)}\n```\n\n"
                    elif isinstance(value, list):
                        prompt += f"## {key.title()}\n"
                        for item in value:
                            prompt += f"- {item}\n"
                        prompt += "\n"
                    else:
                        prompt += f"## {key.title()}\n{value}\n\n"
            
            # Add memories if available
            if context.get("memories"):
                prompt += "## Memories\n"
                for memory in context["memories"]:
                    prompt += f"- {memory}\n"
                prompt += "\n"
            
            # Add previous steps
            if previous_steps:
                prompt += "# Previous Steps\n"
                for i, step_data in enumerate(previous_steps):
                    prompt += f"## Step {i}\n"
                    if "reasoning" in step_data:
                        prompt += f"### Reasoning\n{step_data['reasoning']}\n\n"
                    if "action" in step_data and step_data["action"]:
                        prompt += f"### Action: {step_data['action']}\n"
                        if "action_input" in step_data:
                            prompt += f"Input:\n```json\n{json.dumps(step_data['action_input'], indent=2)}\n```\n\n"
                        if "action_output" in step_data:
                            prompt += f"Output:\n```json\n{json.dumps(step_data['action_output'], indent=2)}\n```\n\n"
            
            # Add instructions
            prompt += """# Instructions
I'm an AI assistant for software engineering tasks. I'll help you solve the task step by step.

For this step, I need to:
1. Analyze the current task state and context
2. Provide reasoning about what to do next
3. Decide if an action is needed and what it should be

If I need to take an action, I'll provide it in the following JSON format:

```action
{
  "name": "tool_name",
  "input": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

If the task is complete, I'll clearly state "Task complete".

I'll start by analyzing the task and context:

"""
        
        else:
            # Generic format for other providers
            prompt = f"Task: {task}\nStep: {step}\n\n"
            
            if context:
                prompt += "Context:\n"
                prompt += json.dumps(context, indent=2) + "\n\n"
            
            if previous_steps:
                prompt += "Previous steps:\n"
                prompt += json.dumps(previous_steps, indent=2) + "\n\n"
            
            prompt += """Instructions:
For this step, analyze the current task state and context, then decide what to do next.
If you need to take an action, format it as:

```action
{
  "name": "tool_name",
  "input": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

If the task is complete, clearly state "Task complete".
"""
        
        return prompt
    
    async def generate(self, prompt: str) -> str:
        """Generate text from the LLM.
        
        Args:
            prompt: The prompt to send to the LLM.
            
        Returns:
            Generated text.
        """
        if self.provider == Provider.OPENAI:
            return await self._generate_openai(prompt)
        elif self.provider == Provider.ANTHROPIC:
            return await self._generate_anthropic(prompt)
        elif self.provider == Provider.OPENROUTER:
            return await self._generate_openrouter(prompt)
        elif self.provider == Provider.AZURE:
            return await self._generate_azure(prompt)
        elif self.provider == Provider.GOOGLE:
            return await self._generate_google(prompt)
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")
    
    async def _generate_openai(self, prompt: str) -> str:
        """Generate text using OpenAI API.
        
        Args:
            prompt: Prompt text.
            
        Returns:
            Generated text.
        """
        api_key = self.model_config.api_key or os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key not found")
        
        model = self.model_config.model or "gpt-3.5-turbo"
        temperature = self.model_config.temperature
        max_tokens = self.model_config.max_tokens
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}",
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens,
        }
        
        if self.model_config.top_p:
            data["top_p"] = self.model_config.top_p
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"OpenAI API error: {error_text}")
                
                response_data = await response.json()
                
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    return response_data["choices"][0]["message"]["content"]
                else:
                    raise ValueError("OpenAI API returned no choices")
    
    async def _generate_anthropic(self, prompt: str) -> str:
        """Generate text using Anthropic API.
        
        Args:
            prompt: Prompt text.
            
        Returns:
            Generated text.
        """
        api_key = self.model_config.api_key or os.environ.get("ANTHROPIC_API_KEY")
        if not api_key:
            raise ValueError("Anthropic API key not found")
        
        model = self.model_config.model or "claude-2"
        temperature = self.model_config.temperature
        max_tokens = self.model_config.max_tokens
        
        headers = {
            "Content-Type": "application/json",
            "x-api-key": api_key,
            "anthropic-version": "2023-06-01",
        }
        
        data = {
            "model": model,
            "prompt": f"\n\nHuman: {prompt}\n\nAssistant:",
            "temperature": temperature,
            "max_tokens_to_sample": max_tokens,
        }
        
        if self.model_config.top_p:
            data["top_p"] = self.model_config.top_p
        
        if self.model_config.top_k:
            data["top_k"] = self.model_config.top_k
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.anthropic.com/v1/complete",
                headers=headers,
                json=data,
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"Anthropic API error: {error_text}")
                
                response_data = await response.json()
                
                if "completion" in response_data:
                    return response_data["completion"]
                else:
                    raise ValueError("Anthropic API returned no completion")
    
    async def _generate_openrouter(self, prompt: str) -> str:
        """Generate text using OpenRouter API.
        
        Args:
            prompt: Prompt text.
            
        Returns:
            Generated text.
        """
        api_key = self.model_config.api_key or os.environ.get("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OpenRouter API key not found")
        
        model = self.model_config.model or "openai/gpt-3.5-turbo"
        temperature = self.model_config.temperature
        max_tokens = self.model_config.max_tokens
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}",
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens,
        }
        
        if self.model_config.top_p:
            data["top_p"] = self.model_config.top_p
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"OpenRouter API error: {error_text}")
                
                response_data = await response.json()
                
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    return response_data["choices"][0]["message"]["content"]
                else:
                    raise ValueError("OpenRouter API returned no choices")
    
    async def _generate_azure(self, prompt: str) -> str:
        """Generate text using Azure OpenAI API.
        
        Args:
            prompt: Prompt text.
            
        Returns:
            Generated text.
        """
        api_key = self.model_config.api_key or os.environ.get("AZURE_API_KEY")
        if not api_key:
            raise ValueError("Azure API key not found")
        
        # Azure requires additional configuration
        azure_endpoint = os.environ.get("AZURE_ENDPOINT")
        azure_deployment = os.environ.get("AZURE_DEPLOYMENT")
        
        if not azure_endpoint or not azure_deployment:
            raise ValueError("Azure endpoint or deployment name not configured")
        
        model = self.model_config.model or "gpt-35-turbo"
        temperature = self.model_config.temperature
        max_tokens = self.model_config.max_tokens
        
        headers = {
            "Content-Type": "application/json",
            "api-key": api_key,
        }
        
        data = {
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens,
        }
        
        if self.model_config.top_p:
            data["top_p"] = self.model_config.top_p
        
        url = f"{azure_endpoint}/openai/deployments/{azure_deployment}/chat/completions?api-version=2023-05-15"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"Azure API error: {error_text}")
                
                response_data = await response.json()
                
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    return response_data["choices"][0]["message"]["content"]
                else:
                    raise ValueError("Azure API returned no choices")
    
    async def _generate_google(self, prompt: str) -> str:
        """Generate text using Google API.
        
        Args:
            prompt: Prompt text.
            
        Returns:
            Generated text.
        """
        api_key = self.model_config.api_key or os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("Google API key not found")
        
        model = self.model_config.model or "gemini-pro"
        temperature = self.model_config.temperature
        max_tokens = self.model_config.max_tokens
        
        headers = {
            "Content-Type": "application/json",
        }
        
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": temperature,
                "maxOutputTokens": max_tokens,
                "topP": self.model_config.top_p,
            },
        }
        
        url = f"https://generativelanguage.googleapis.com/v1/models/{model}:generateContent?key={api_key}"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"Google API error: {error_text}")
                
                response_data = await response.json()
                
                if "candidates" in response_data and len(response_data["candidates"]) > 0:
                    return response_data["candidates"][0]["content"]["parts"][0]["text"]
                else:
                    raise ValueError("Google API returned no candidates")
    
    async def generate_summary(self, data: Dict[str, Any]) -> str:
        """Generate a summary of task execution.
        
        Args:
            data: Data to summarize.
            
        Returns:
            Summary text.
        """
        task = data.get("task", "")
        steps = data.get("steps", [])
        
        prompt = f"""Please provide a concise summary of the following task execution:

Task: {task}

Steps Taken:
"""
        
        for step in steps:
            prompt += f"- Step {step.get('step', '')}: "
            if step.get("action"):
                prompt += f"Executed {step.get('action')} "
            prompt += f"{step.get('reasoning', '')[:100]}...\n"
        
        prompt += "\nGenerate a concise summary focusing on what was accomplished, any key decisions made, and the final outcome of the task. Keep it brief and informative."
        
        return await self.generate(prompt)
