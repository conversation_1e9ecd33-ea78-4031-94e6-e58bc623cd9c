"""Memory management for CLI Agent."""

import json
import os
import sqlite3
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from .config import Config


class Memory:
    """Memory system for CLI Agent."""
    
    def __init__(self, config: Config):
        """Initialize the memory system.
        
        Args:
            config: Agent configuration.
        """
        self.config = config
        
        # Get database path from config
        self.db_path = config.memory_db_path or "memory.db"
        
        # Create directory if it doesn't exist
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # Create tables if they don't exist
        self._init_db()
        
        # Session memory (in-memory storage for current session)
        self.session_memory = {}
    
    def _init_db(self) -> None:
        """Initialize the SQLite database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tasks table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tasks (
                task_id TEXT PRIMARY KEY,
                task TEXT NOT NULL,
                created_at REAL NOT NULL,
                updated_at REAL NOT NULL
            )
        """)
        
        # Create memories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS memories (
                memory_id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                created_at REAL NOT NULL,
                FOREIGN KEY (task_id) REFERENCES tasks (task_id) ON DELETE CASCADE
            )
        """)
        
        # Create general memories table (not tied to a specific task)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS general_memories (
                memory_id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                created_at REAL NOT NULL
            )
        """)
        
        conn.commit()
        conn.close()
    
    def init_task_memory(self, task_id: str) -> None:
        """Initialize memory for a task.
        
        Args:
            task_id: Task ID.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if task already exists
            cursor.execute("SELECT task_id FROM tasks WHERE task_id = ?", (task_id,))
            if not cursor.fetchone():
                # Create task record
                now = time.time()
                cursor.execute(
                    "INSERT INTO tasks (task_id, task, created_at, updated_at) VALUES (?, ?, ?, ?)",
                    (task_id, "", now, now),
                )
                conn.commit()
            
            # Initialize session memory for this task
            self.session_memory[task_id] = {}
            
            conn.close()
        except Exception as e:
            print(f"Error initializing task memory: {e}")
    
    def add_memory(self, task_id: str, key: str, value: Any) -> bool:
        """Add a memory for a task.
        
        Args:
            task_id: Task ID.
            key: Memory key.
            value: Memory value.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            # Convert value to string if needed
            if not isinstance(value, str):
                if isinstance(value, dict) or isinstance(value, list):
                    value = json.dumps(value)
                else:
                    value = str(value)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Add to database
            cursor.execute(
                "INSERT INTO memories (task_id, key, value, created_at) VALUES (?, ?, ?, ?)",
                (task_id, key, value, time.time()),
            )
            conn.commit()
            
            # Add to session memory
            if task_id not in self.session_memory:
                self.session_memory[task_id] = {}
            self.session_memory[task_id][key] = value
            
            conn.close()
            return True
        
        except Exception as e:
            print(f"Error adding memory: {e}")
            return False
    
    def add_general_memory(self, key: str, value: Any) -> bool:
        """Add a general memory not tied to a specific task.
        
        Args:
            key: Memory key.
            value: Memory value.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            # Convert value to string if needed
            if not isinstance(value, str):
                if isinstance(value, dict) or isinstance(value, list):
                    value = json.dumps(value)
                else:
                    value = str(value)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Add to database
            cursor.execute(
                "INSERT INTO general_memories (key, value, created_at) VALUES (?, ?, ?)",
                (key, value, time.time()),
            )
            conn.commit()
            conn.close()
            
            return True
        
        except Exception as e:
            print(f"Error adding general memory: {e}")
            return False
    
    def get_memory(self, task_id: str, key: str) -> Optional[str]:
        """Get a memory value for a task.
        
        Args:
            task_id: Task ID.
            key: Memory key.
            
        Returns:
            Memory value or None if not found.
        """
        # Check session memory first
        if task_id in self.session_memory and key in self.session_memory[task_id]:
            return self.session_memory[task_id][key]
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get from database
            cursor.execute(
                "SELECT value FROM memories WHERE task_id = ? AND key = ? ORDER BY created_at DESC LIMIT 1",
                (task_id, key),
            )
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                # Cache in session memory
                if task_id not in self.session_memory:
                    self.session_memory[task_id] = {}
                self.session_memory[task_id][key] = result[0]
                
                return result[0]
            
            return None
        
        except Exception as e:
            print(f"Error getting memory: {e}")
            return None
    
    def get_general_memory(self, key: str) -> Optional[str]:
        """Get a general memory value.
        
        Args:
            key: Memory key.
            
        Returns:
            Memory value or None if not found.
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get from database
            cursor.execute(
                "SELECT value FROM general_memories WHERE key = ? ORDER BY created_at DESC LIMIT 1",
                (key,),
            )
            result = cursor.fetchone()
            
            conn.close()
            
            return result[0] if result else None
        
        except Exception as e:
            print(f"Error getting general memory: {e}")
            return None
    
    def get_all_memories(self, task_id: str) -> Dict[str, str]:
        """Get all memories for a task.
        
        Args:
            task_id: Task ID.
            
        Returns:
            Dictionary of memory key-value pairs.
        """
        memories = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all memories for this task
            cursor.execute(
                "SELECT key, value FROM memories WHERE task_id = ? ORDER BY created_at DESC",
                (task_id,),
            )
            
            # Handle multiple entries with the same key by keeping only the most recent
            seen_keys = set()
            for key, value in cursor.fetchall():
                if key not in seen_keys:
                    memories[key] = value
                    seen_keys.add(key)
            
            conn.close()
            
            # Update session memory
            if task_id not in self.session_memory:
                self.session_memory[task_id] = {}
            self.session_memory[task_id].update(memories)
            
            return memories
        
        except Exception as e:
            print(f"Error getting all memories: {e}")
            return {}
    
    def get_relevant_memories(self, query: str, limit: int = 5) -> List[str]:
        """Get memories relevant to a query.
        
        Args:
            query: Query string.
            limit: Maximum number of memories to return.
            
        Returns:
            List of relevant memory values.
        """
        # This is a simple implementation - in a real system, you'd use
        # vector embeddings and semantic similarity
        
        relevant_memories = []
        
        try:
            # Convert query to lowercase for case-insensitive matching
            query_lower = query.lower()
            
            # Get keywords from query (simple approach)
            words = query_lower.split()
            keywords = [w for w in words if len(w) > 3 and w not in {"with", "this", "that", "what", "when", "where", "which", "than"}]
            
            conn = sqlite3.connect(self.db_path)
            conn.create_function("LOWER", 1, lambda x: x.lower() if x else None)
            cursor = conn.cursor()
            
            # Get memories from both task memories and general memories
            all_memories = []
            
            # Query for each keyword
            for keyword in keywords:
                # Search in task memories
                cursor.execute(
                    """
                    SELECT value, COUNT(*) as match_count
                    FROM memories
                    WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?
                    GROUP BY value
                    ORDER BY match_count DESC
                    LIMIT ?
                    """,
                    (f"%{keyword}%", f"%{keyword}%", limit)
                )
                all_memories.extend(row[0] for row in cursor.fetchall())
                
                # Search in general memories
                cursor.execute(
                    """
                    SELECT value, COUNT(*) as match_count
                    FROM general_memories
                    WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?
                    GROUP BY value
                    ORDER BY match_count DESC
                    LIMIT ?
                    """,
                    (f"%{keyword}%", f"%{keyword}%", limit)
                )
                all_memories.extend(row[0] for row in cursor.fetchall())
            
            conn.close()
            
            # Deduplicate and limit results
            seen = set()
            for memory in all_memories:
                if memory not in seen and len(relevant_memories) < limit:
                    relevant_memories.append(memory)
                    seen.add(memory)
            
            return relevant_memories
        
        except Exception as e:
            print(f"Error getting relevant memories: {e}")
            return []
    
    def clear_session_memory(self) -> None:
        """Clear session memory."""
        self.session_memory = {}
