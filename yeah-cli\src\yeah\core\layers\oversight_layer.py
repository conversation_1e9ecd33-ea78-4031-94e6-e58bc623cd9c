"""
Oversight Layer - Monitors, reviews, and ensures adherence to requirements
Part of Kiro's Four-Layer Agentic Architecture
"""

from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from datetime import datetime
from pathlib import Path

class ValidationResult:
    """Result of a validation check"""
    
    def __init__(self):
        self.passed: bool = False
        self.validator_name: str = ""
        self.message: str = ""
        self.severity: str = "info"  # info, warning, error
        self.details: Dict[str, Any] = {}

class OversightReport:
    """Comprehensive oversight report"""
    
    def __init__(self):
        self.timestamp: datetime = datetime.now()
        self.overall_status: str = "pending"  # pending, approved, rejected
        self.validations: List[ValidationResult] = []
        self.recommendations: List[str] = []
        self.risk_assessment: Dict[str, Any] = {}
        self.compliance_score: float = 0.0

class OperationMode(Enum):
    """Operation modes for oversight"""
    SUPERVISED = "supervised"  # Requires user approval
    AUTOPILOT = "autopilot"   # Autonomous with monitoring

class OversightLayer:
    """
    Oversight Layer implementation following <PERSON><PERSON>'s principles:
    - Monitors and reviews all actions before execution
    - Ensures adherence to technical and organizational requirements
    - Provides feedback loops for verifying results
    - Supports both supervised and autonomous operation modes
    """
    
    def __init__(self, mode: OperationMode = OperationMode.SUPERVISED):
        self.mode = mode
        self.validators: List[Callable] = []
        self.approval_threshold: float = 0.8  # Minimum compliance score for auto-approval
        self.oversight_history: List[OversightReport] = []
        
        # Register default validators
        self._register_default_validators()
    
    def review_execution_plan(self, plan, intent, knowledge) -> OversightReport:
        """
        Review an execution plan before it's executed
        """
        report = OversightReport()
        
        # Run all validators
        for validator in self.validators:
            try:
                result = validator(plan, intent, knowledge)
                if isinstance(result, ValidationResult):
                    report.validations.append(result)
                elif isinstance(result, list):
                    report.validations.extend(result)
            except Exception as e:
                error_result = ValidationResult()
                error_result.validator_name = validator.__name__
                error_result.passed = False
                error_result.severity = "error"
                error_result.message = f"Validator failed: {str(e)}"
                report.validations.append(error_result)
        
        # Calculate compliance score
        report.compliance_score = self._calculate_compliance_score(report.validations)
        
        # Generate recommendations
        report.recommendations = self._generate_recommendations(report.validations)
        
        # Assess risk
        report.risk_assessment = self._assess_risk(plan, report.validations)
        
        # Determine overall status
        report.overall_status = self._determine_status(report, plan)
        
        # Store in history
        self.oversight_history.append(report)
        
        return report
    
    def requires_approval(self, report: OversightReport) -> bool:
        """
        Determine if the plan requires explicit user approval
        """
        if self.mode == OperationMode.SUPERVISED:
            return True
        
        # In autopilot mode, require approval for high-risk or low-compliance actions
        if report.compliance_score < self.approval_threshold:
            return True
        
        if report.risk_assessment.get('level') == 'high':
            return True
        
        # Check for critical validation failures
        critical_failures = [v for v in report.validations if not v.passed and v.severity == 'error']
        if critical_failures:
            return True
        
        return False
    
    def register_validator(self, validator: Callable):
        """Register a custom validator"""
        self.validators.append(validator)
    
    def _register_default_validators(self):
        """Register default validation functions"""
        self.validators.extend([
            self._validate_agent_os_compliance,
            self._validate_file_safety,
            self._validate_standards_adherence,
            self._validate_spec_alignment,
            self._validate_security_concerns
        ])
    
    def _validate_agent_os_compliance(self, plan, intent, knowledge) -> List[ValidationResult]:
        """Validate compliance with Agent OS principles"""
        results = []
        
        # Check if structured workflow is being followed
        result = ValidationResult()
        result.validator_name = "agent_os_compliance"
        
        if intent.intent_type.value == "structured":
            result.passed = True
            result.message = "Following structured Agent OS workflow"
        else:
            result.passed = False
            result.severity = "warning"
            result.message = "Unstructured request - consider using Agent OS workflow"
        
        results.append(result)
        
        # Check for required documentation
        if intent.category.value in ["create_spec", "execute_tasks"]:
            doc_result = ValidationResult()
            doc_result.validator_name = "documentation_check"
            
            required_docs = ["requirements.md", "design.md", "tasks.md"]
            has_docs = any(doc in intent.structured_docs for doc in required_docs)
            
            doc_result.passed = has_docs
            doc_result.message = "Required documentation present" if has_docs else "Missing required documentation"
            doc_result.severity = "error" if not has_docs else "info"
            
            results.append(doc_result)
        
        return results
    
    def _validate_file_safety(self, plan, intent, knowledge) -> ValidationResult:
        """Validate file operations for safety"""
        result = ValidationResult()
        result.validator_name = "file_safety"
        
        dangerous_operations = []
        
        for action in plan.actions:
            # Check for dangerous file operations
            if action['type'] == 'delete_file':
                path = action.get('path', '')
                if not path.startswith('.agent-os/') and not path.startswith('temp/'):
                    dangerous_operations.append(f"Deleting {path}")
            
            # Check for system file modifications
            if action['type'] in ['create_file', 'modify_file']:
                path = action.get('path', '')
                if any(path.startswith(sys_path) for sys_path in ['/etc/', '/usr/', '/bin/', 'C:\\Windows\\']):
                    dangerous_operations.append(f"Modifying system file {path}")
        
        if dangerous_operations:
            result.passed = False
            result.severity = "error"
            result.message = f"Dangerous operations detected: {', '.join(dangerous_operations)}"
        else:
            result.passed = True
            result.message = "File operations appear safe"
        
        return result
    
    def _validate_standards_adherence(self, plan, intent, knowledge) -> ValidationResult:
        """Validate adherence to coding standards"""
        result = ValidationResult()
        result.validator_name = "standards_adherence"
        
        # Check if standards are available
        standards = knowledge.standards
        if not standards:
            result.passed = False
            result.severity = "warning"
            result.message = "No coding standards found"
            return result
        
        # For code generation actions, check if standards are being applied
        code_actions = [a for a in plan.actions if a['type'] == 'generate_code']
        if code_actions:
            # This would need more sophisticated checking in a real implementation
            result.passed = True
            result.message = f"Standards will be applied to {len(code_actions)} code generation actions"
        else:
            result.passed = True
            result.message = "No code generation actions to validate"
        
        return result
    
    def _validate_spec_alignment(self, plan, intent, knowledge) -> ValidationResult:
        """Validate alignment with active specifications"""
        result = ValidationResult()
        result.validator_name = "spec_alignment"
        
        if intent.category.value == "execute_tasks":
            active_specs = knowledge.codebase_state.get('active_specs', [])
            
            if not active_specs:
                result.passed = False
                result.severity = "error"
                result.message = "No active specifications found for task execution"
            else:
                result.passed = True
                result.message = f"Aligned with {len(active_specs)} active specification(s)"
        else:
            result.passed = True
            result.message = "Spec alignment not required for this action type"
        
        return result
    
    def _validate_security_concerns(self, plan, intent, knowledge) -> ValidationResult:
        """Validate for security concerns"""
        result = ValidationResult()
        result.validator_name = "security_check"
        
        security_issues = []
        
        for action in plan.actions:
            # Check for command execution
            if action['type'] == 'run_command':
                command = action.get('command', '')
                if any(dangerous in command.lower() for dangerous in ['rm -rf', 'del /f', 'format', 'sudo']):
                    security_issues.append(f"Dangerous command: {command}")
            
            # Check for network operations
            if 'url' in action or 'download' in action.get('type', ''):
                security_issues.append("Network operation detected")
        
        if security_issues:
            result.passed = False
            result.severity = "error"
            result.message = f"Security concerns: {', '.join(security_issues)}"
        else:
            result.passed = True
            result.message = "No security concerns detected"
        
        return result
    
    def _calculate_compliance_score(self, validations: List[ValidationResult]) -> float:
        """Calculate overall compliance score"""
        if not validations:
            return 0.0
        
        total_weight = 0
        weighted_score = 0
        
        for validation in validations:
            # Weight based on severity
            weight = 1.0
            if validation.severity == "error":
                weight = 3.0
            elif validation.severity == "warning":
                weight = 2.0
            
            total_weight += weight
            if validation.passed:
                weighted_score += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_recommendations(self, validations: List[ValidationResult]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        failed_validations = [v for v in validations if not v.passed]
        
        for validation in failed_validations:
            if validation.validator_name == "agent_os_compliance":
                recommendations.append("Consider using structured Agent OS workflows for better consistency")
            elif validation.validator_name == "documentation_check":
                recommendations.append("Create required documentation (requirements.md, design.md, tasks.md)")
            elif validation.validator_name == "file_safety":
                recommendations.append("Review file operations for safety concerns")
            elif validation.validator_name == "security_check":
                recommendations.append("Address security concerns before proceeding")
        
        return recommendations
    
    def _assess_risk(self, plan, validations: List[ValidationResult]) -> Dict[str, Any]:
        """Assess overall risk of the execution plan"""
        risk_factors = []
        risk_score = 0
        
        # Risk from validation failures
        error_count = len([v for v in validations if not v.passed and v.severity == "error"])
        warning_count = len([v for v in validations if not v.passed and v.severity == "warning"])
        
        risk_score += error_count * 3 + warning_count * 1
        
        # Risk from action types
        high_risk_actions = [a for a in plan.actions if a['type'] in ['delete_file', 'run_command']]
        risk_score += len(high_risk_actions) * 2
        
        # Risk from action count
        if len(plan.actions) > 20:
            risk_score += 2
            risk_factors.append("Large number of actions")
        
        # Determine risk level
        if risk_score >= 10:
            level = "high"
        elif risk_score >= 5:
            level = "medium"
        else:
            level = "low"
        
        return {
            'level': level,
            'score': risk_score,
            'factors': risk_factors,
            'error_count': error_count,
            'warning_count': warning_count
        }
    
    def _determine_status(self, report: OversightReport, plan) -> str:
        """Determine the overall status of the oversight review"""
        
        # Check for critical failures
        critical_failures = [v for v in report.validations if not v.passed and v.severity == "error"]
        if critical_failures:
            return "rejected"
        
        # Check compliance score
        if report.compliance_score < 0.5:
            return "rejected"
        
        # Check if approval is required
        if self.requires_approval(report):
            return "pending"
        
        # Auto-approve if all checks pass
        return "approved"
