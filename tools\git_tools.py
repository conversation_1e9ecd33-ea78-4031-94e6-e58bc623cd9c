"""Git tools for CLI Agent."""

import os
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional

from .base import Tool
from .shell_tools import RunShellCommandTool


class GetGitInfoTool(Tool):
    """Tool for retrieving Git repository information."""
    
    @property
    def name(self) -> str:
        return "git_info"
    
    @property
    def description(self) -> str:
        return "Get information about a Git repository, including status, branches, and recent commits."
    
    async def execute(
        self,
        repo_path: Optional[str] = None,
        include_diff: bool = False,
        num_commits: int = 5,
    ) -> Dict[str, Any]:
        """Get Git repository information.
        
        Args:
            repo_path: Path to the Git repository. Defaults to current working directory.
            include_diff: Whether to include the current diff.
            num_commits: Number of recent commits to include.
            
        Returns:
            Dictionary with Git repository information.
        """
        try:
            path = repo_path or os.getcwd()
            
            # Check if this is a Git repository
            git_dir = os.path.join(path, ".git")
            if not os.path.exists(git_dir):
                return {"error": f"Not a Git repository: {path}"}
            
            # Use RunShellCommandTool for command execution
            shell_tool = RunShellCommandTool()
            result = {}
            
            # Get current branch
            branch_cmd = await shell_tool.execute("git branch --show-current", cwd=path)
            if branch_cmd["success"]:
                result["current_branch"] = branch_cmd["stdout"].strip()
            
            # Get all branches
            branches_cmd = await shell_tool.execute(
                "git branch --list", cwd=path
            )
            if branches_cmd["success"]:
                result["branches"] = [
                    b.strip() for b in branches_cmd["stdout"].split("\n") if b.strip()
                ]
            
            # Get recent commits
            commits_cmd = await shell_tool.execute(
                f"git log -n {num_commits} --pretty=format:'%H|%an|%ad|%s'",
                cwd=path,
            )
            if commits_cmd["success"]:
                commits = []
                for line in commits_cmd["stdout"].split("\n"):
                    if line.strip():
                        parts = line.split("|", 3)
                        if len(parts) >= 4:
                            commits.append({
                                "hash": parts[0],
                                "author": parts[1],
                                "date": parts[2],
                                "message": parts[3],
                            })
                result["recent_commits"] = commits
            
            # Get status
            status_cmd = await shell_tool.execute("git status --porcelain", cwd=path)
            if status_cmd["success"]:
                status = []
                for line in status_cmd["stdout"].split("\n"):
                    if line.strip():
                        status_code = line[:2]
                        file_path = line[3:].strip()
                        status.append({"status": status_code, "path": file_path})
                result["status"] = status
                
                # Count changes
                result["changes"] = {
                    "modified": len([s for s in status if s["status"].startswith("M")]),
                    "added": len([s for s in status if s["status"].startswith("A")]),
                    "deleted": len([s for s in status if s["status"].startswith("D")]),
                    "renamed": len([s for s in status if s["status"].startswith("R")]),
                    "untracked": len([s for s in status if s["status"].startswith("??")]),
                }
            
            # Get diff if requested
            if include_diff:
                diff_cmd = await shell_tool.execute("git diff", cwd=path)
                if diff_cmd["success"]:
                    result["diff"] = diff_cmd["stdout"]
            
            # Get remote info
            remote_cmd = await shell_tool.execute("git remote -v", cwd=path)
            if remote_cmd["success"]:
                remotes = {}
                for line in remote_cmd["stdout"].split("\n"):
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            name = parts[0]
                            url = parts[1]
                            remotes[name] = url
                result["remotes"] = remotes
            
            return result
        
        except Exception as e:
            return {"error": f"Error getting Git info: {str(e)}"}
