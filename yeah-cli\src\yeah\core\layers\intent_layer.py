"""
Intent Layer - Captures user input and parses intent
Part of Kiro's Four-Layer Agentic Architecture
"""

import re
from typing import Dict, List, Optional, Union, Any
from enum import Enum
from pathlib import Path

class IntentType(Enum):
    """Types of user intent"""
    STRUCTURED = "structured"      # Spec-driven, follows Agent OS workflows
    UNSTRUCTURED = "unstructured"  # Conversational, ad-hoc requests
    HYBRID = "hybrid"             # Mix of structured and unstructured

class RequestCategory(Enum):
    """Categories of user requests"""
    PLAN_PRODUCT = "plan_product"
    CREATE_SPEC = "create_spec"
    EXECUTE_TASKS = "execute_tasks"
    ANALYZE_PRODUCT = "analyze_product"
    GENERAL_QUERY = "general_query"
    CODE_MODIFICATION = "code_modification"
    DOCUMENTATION = "documentation"

class ParsedIntent:
    """Structured representation of user intent"""
    
    def __init__(self):
        self.intent_type: IntentType = IntentType.UNSTRUCTURED
        self.category: RequestCategory = RequestCategory.GENERAL_QUERY
        self.primary_action: str = ""
        self.parameters: Dict[str, Any] = {}
        self.context_requirements: List[str] = []
        self.structured_docs: Dict[str, str] = {}  # requirements.md, design.md, tasks.md
        self.confidence: float = 0.0
        self.raw_input: str = ""

class IntentLayer:
    """
    Intent Layer implementation following Kiro's principles:
    - Distinguishes between structured and unstructured requests
    - Codifies structured intent through requirements, design, and task documents
    - Minimizes misinterpretation during handoff to execution
    """
    
    def __init__(self):
        # Enhanced command patterns with more sophisticated matching
        self.command_patterns = {
            RequestCategory.PLAN_PRODUCT: [
                r'/plan-product',
                r'plan\s+(?:a\s+)?(?:new\s+)?product',
                r'initialize\s+(?:a\s+)?(?:new\s+)?project',
                r'start\s+(?:a\s+)?new\s+product',
                r'create\s+(?:a\s+)?new\s+product',
                r'set\s+up\s+(?:a\s+)?(?:new\s+)?project',
                r'bootstrap\s+(?:a\s+)?project'
            ],
            RequestCategory.CREATE_SPEC: [
                r'/create-spec',
                r'create\s+(?:a\s+)?spec(?:ification)?',
                r'plan\s+(?:a\s+)?feature',
                r'write\s+(?:a\s+)?specification',
                r'design\s+(?:a\s+)?feature',
                r'spec\s+out\s+(?:a\s+)?feature',
                r'document\s+requirements\s+for'
            ],
            RequestCategory.EXECUTE_TASKS: [
                r'/execute-tasks?',
                r'execute\s+tasks?',
                r'implement\s+(?:the\s+)?feature',
                r'build\s+(?:the\s+)?code',
                r'develop\s+(?:the\s+)?feature',
                r'code\s+(?:the\s+)?implementation',
                r'work\s+on\s+tasks?',
                r'start\s+development'
            ],
            RequestCategory.ANALYZE_PRODUCT: [
                r'/analyze-product',
                r'analyze\s+(?:the\s+)?product',
                r'review\s+(?:the\s+)?codebase',
                r'audit\s+(?:the\s+)?project',
                r'examine\s+(?:the\s+)?code',
                r'assess\s+(?:the\s+)?project',
                r'evaluate\s+(?:the\s+)?codebase'
            ]
        }

        # Structured indicators that suggest spec-driven approach
        self.structured_indicators = {
            'explicit_commands': ['/plan-product', '/create-spec', '/execute-tasks', '/analyze-product'],
            'workflow_terms': ['spec', 'specification', 'requirements', 'design', 'tasks', 'workflow'],
            'agent_os_terms': ['agent os', 'agent-os', 'structured', 'systematic'],
            'documentation_terms': ['document', 'requirements.md', 'design.md', 'tasks.md'],
            'process_terms': ['process', 'methodology', 'framework', 'systematic approach']
        }

        # Unstructured indicators that suggest conversational approach
        self.unstructured_indicators = {
            'conversational': ['help me', 'can you', 'how do i', 'what is', 'explain'],
            'exploratory': ['explore', 'investigate', 'look into', 'research'],
            'informal': ['quick', 'just', 'simply', 'easy way'],
            'questions': ['?', 'how', 'what', 'why', 'when', 'where']
        }
    
    def parse_intent(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> ParsedIntent:
        """
        Parse user input to determine intent type and extract structured information
        """
        intent = ParsedIntent()
        intent.raw_input = user_input.strip()
        
        # Determine intent type and category
        intent.category = self._classify_request(user_input)
        intent.intent_type = self._determine_intent_type(user_input, context)
        
        # Extract parameters and requirements
        intent.parameters = self._extract_parameters(user_input, intent.category)
        intent.context_requirements = self._identify_context_requirements(intent.category)
        
        # For structured requests, prepare document templates
        if intent.intent_type == IntentType.STRUCTURED:
            intent.structured_docs = self._prepare_structured_docs(intent.category, intent.parameters)
        
        # Calculate confidence score
        intent.confidence = self._calculate_confidence(intent)
        
        return intent
    
    def _classify_request(self, user_input: str) -> RequestCategory:
        """Classify the user request into a category"""
        user_input_lower = user_input.lower()
        
        for category, patterns in self.command_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_input_lower):
                    return category
        
        # Fallback classification based on keywords
        if any(word in user_input_lower for word in ['implement', 'code', 'build', 'create']):
            return RequestCategory.CODE_MODIFICATION
        elif any(word in user_input_lower for word in ['document', 'docs', 'readme']):
            return RequestCategory.DOCUMENTATION
        
        return RequestCategory.GENERAL_QUERY
    
    def _determine_intent_type(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentType:
        """Determine if the request is structured, unstructured, or hybrid using enhanced analysis"""

        user_input_lower = user_input.lower()

        # Score different intent types
        structured_score = 0
        unstructured_score = 0

        # Check for explicit Agent OS commands (highest weight for structured)
        for cmd in self.structured_indicators['explicit_commands']:
            if user_input.startswith(cmd):
                structured_score += 10

        # Check for structured indicators with different weights
        for category, terms in self.structured_indicators.items():
            if category == 'explicit_commands':
                continue  # Already handled above

            weight = 3 if category == 'workflow_terms' else 2
            for term in terms:
                if term in user_input_lower:
                    structured_score += weight

        # Check for unstructured indicators
        for category, terms in self.unstructured_indicators.items():
            weight = 3 if category == 'conversational' else 2
            for term in terms:
                if term in user_input_lower:
                    unstructured_score += weight

        # Context-based adjustments
        if context:
            # If project has Agent OS structure, bias toward structured
            if self._has_agent_os_structure(context):
                structured_score += 2

            # If there are active specs, bias toward structured for implementation requests
            if context.get('active_specs') and any(word in user_input_lower for word in ['implement', 'build', 'code']):
                structured_score += 3

        # Determine intent type based on scores
        if structured_score >= 5 and structured_score > unstructured_score * 1.5:
            return IntentType.STRUCTURED
        elif unstructured_score > structured_score * 1.5:
            return IntentType.UNSTRUCTURED
        elif structured_score > 0 and unstructured_score > 0:
            return IntentType.HYBRID
        elif structured_score > 0:
            return IntentType.STRUCTURED
        else:
            return IntentType.UNSTRUCTURED
    
    def _extract_parameters(self, user_input: str, category: RequestCategory) -> Dict[str, Any]:
        """Extract parameters specific to the request category with enhanced parsing"""
        parameters: Dict[str, Any] = {}

        # Enhanced feature name extraction for CREATE_SPEC
        if category == RequestCategory.CREATE_SPEC:
            # Multiple patterns for feature name extraction
            feature_patterns = [
                r'(?:spec\s+for|create\s+spec\s+for|plan\s+feature)\s+([a-zA-Z0-9\s\-_]+)',
                r'(?:feature\s+called|feature\s+named)\s+([a-zA-Z0-9\s\-_]+)',
                r'(?:implement|build|develop)\s+([a-zA-Z0-9\s\-_]+)\s+feature',
                r'/create-spec\s+([a-zA-Z0-9\s\-_]+)',
                r'spec\s+([a-zA-Z0-9\s\-_]+)'
            ]

            for pattern in feature_patterns:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    feature_name = match.group(1).strip()
                    # Clean up the feature name
                    feature_name = re.sub(r'\s+', '-', feature_name.lower())
                    parameters['feature_name'] = feature_name
                    break

        # Enhanced task extraction for EXECUTE_TASKS
        elif category == RequestCategory.EXECUTE_TASKS:
            # Extract specific task references
            task_patterns = [
                r'task\s+([a-zA-Z0-9\-_]+)',
                r'execute\s+task\s+([a-zA-Z0-9\-_]+)',
                r'work\s+on\s+task\s+([a-zA-Z0-9\-_]+)',
                r'implement\s+task\s+([a-zA-Z0-9\-_]+)'
            ]

            for pattern in task_patterns:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    parameters['task_id'] = match.group(1).strip()
                    break

            # Extract priority indicators
            if any(word in user_input.lower() for word in ['urgent', 'priority', 'important']):
                parameters['priority'] = 'high'
            elif any(word in user_input.lower() for word in ['low priority', 'when possible']):
                parameters['priority'] = 'low'
            else:
                parameters['priority'] = 'normal'

        # Enhanced file reference extraction
        file_patterns = [
            r'@([a-zA-Z0-9\-_/.]+\.md)',  # @file.md format
            r'file\s+([a-zA-Z0-9\-_/.]+\.md)',  # file filename.md
            r'document\s+([a-zA-Z0-9\-_/.]+\.md)',  # document filename.md
            r'([a-zA-Z0-9\-_/.]+\.md)\s+file'  # filename.md file
        ]

        referenced_files = []
        for pattern in file_patterns:
            matches = re.findall(pattern, user_input, re.IGNORECASE)
            referenced_files.extend(matches)

        if referenced_files:
            parameters['referenced_files'] = list(set(referenced_files))  # Remove duplicates

        # Extract technology/language preferences
        tech_patterns = [
            r'using\s+([a-zA-Z0-9\-_]+)',
            r'with\s+([a-zA-Z0-9\-_]+)',
            r'in\s+([a-zA-Z0-9\-_]+)',
            r'([a-zA-Z0-9\-_]+)\s+implementation'
        ]

        technologies = []
        for pattern in tech_patterns:
            matches = re.findall(pattern, user_input, re.IGNORECASE)
            # Filter out common words that aren't technologies
            common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'for', 'with', 'using'}
            tech_matches = [match for match in matches if match.lower() not in common_words]
            technologies.extend(tech_matches)

        if technologies:
            parameters['technologies'] = list(set(technologies))

        # Extract scope indicators
        scope_indicators = {
            'small': ['small', 'minor', 'quick', 'simple'],
            'medium': ['medium', 'moderate', 'standard'],
            'large': ['large', 'major', 'complex', 'comprehensive', 'full']
        }

        for scope, indicators in scope_indicators.items():
            if any(indicator in user_input.lower() for indicator in indicators):
                parameters['scope'] = scope
                break

        # Extract timeline indicators
        timeline_patterns = [
            r'by\s+([a-zA-Z0-9\s\-_]+)',
            r'within\s+([a-zA-Z0-9\s\-_]+)',
            r'in\s+(\d+)\s+(days?|weeks?|months?)',
            r'deadline\s+([a-zA-Z0-9\s\-_]+)'
        ]

        for pattern in timeline_patterns:
            match = re.search(pattern, user_input, re.IGNORECASE)
            if match:
                parameters['timeline'] = match.group(1).strip()
                break

        return parameters
    
    def _identify_context_requirements(self, category: RequestCategory) -> List[str]:
        """Identify what context is needed for this type of request"""
        base_requirements = ['mission.md', 'tech-stack.md', 'standards']
        
        category_requirements: Dict[RequestCategory, List[str]] = {
            RequestCategory.PLAN_PRODUCT: [],
            RequestCategory.CREATE_SPEC: ['roadmap.md', 'existing_specs'],
            RequestCategory.EXECUTE_TASKS: ['active_spec', 'tasks.md', 'codebase_state'],
            RequestCategory.ANALYZE_PRODUCT: ['full_codebase', 'git_history'],
            RequestCategory.CODE_MODIFICATION: ['active_spec', 'codebase_state'],
            RequestCategory.DOCUMENTATION: ['codebase_state']
        }
        
        return base_requirements + category_requirements.get(category, [])
    
    def _prepare_structured_docs(self, category: RequestCategory, parameters: Dict[str, Any]) -> Dict[str, str]:
        """Prepare structured document templates for spec-driven development"""
        docs = {}
        
        if category in [RequestCategory.CREATE_SPEC, RequestCategory.EXECUTE_TASKS]:
            docs['requirements.md'] = self._get_requirements_template(parameters)
            docs['design.md'] = self._get_design_template(parameters)
            docs['tasks.md'] = self._get_tasks_template(parameters)
        
        return docs
    
    def _get_requirements_template(self, parameters: Dict[str, Any]) -> str:
        """Generate requirements.md template"""
        feature_name = parameters.get('feature_name', 'New Feature')
        return f"""# Requirements: {feature_name}

## User Stories
- As a [user type], I want [goal] so that [benefit]

## Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2

## Non-Functional Requirements
- Performance: [specify]
- Security: [specify]
- Usability: [specify]
"""
    
    def _get_design_template(self, parameters: Dict[str, Any]) -> str:
        """Generate design.md template"""
        return """# Technical Design

## Architecture Overview
[Describe the high-level architecture]

## API Design
[Define interfaces and contracts]

## Data Models
[Specify data structures]

## Dependencies
[List external dependencies]
"""
    
    def _get_tasks_template(self, parameters: Dict[str, Any]) -> str:
        """Generate tasks.md template"""
        return """# Implementation Tasks

## Phase 1: Foundation
- [ ] Task 1: [Description]
- [ ] Task 2: [Description]

## Phase 2: Core Features
- [ ] Task 3: [Description]
- [ ] Task 4: [Description]

## Phase 3: Testing & Polish
- [ ] Task 5: [Description]
- [ ] Task 6: [Description]
"""
    
    def _has_agent_os_structure(self, context: Dict[str, Any]) -> bool:
        """Check if the project has Agent OS structure"""
        required_files = ['mission.md', 'tech-stack.md', 'roadmap.md']
        return all(file in context.get('product_files', []) for file in required_files)
    
    def _calculate_confidence(self, intent: ParsedIntent) -> float:
        """Calculate confidence score for the parsed intent"""
        confidence = 0.5  # Base confidence
        
        # Boost confidence for explicit commands
        if intent.raw_input.startswith('/'):
            confidence += 0.4
        
        # Boost confidence for structured requests with context
        if intent.intent_type == IntentType.STRUCTURED:
            confidence += 0.3
        
        # Boost confidence if parameters were extracted
        if intent.parameters:
            confidence += 0.2
        
        return min(confidence, 1.0)
