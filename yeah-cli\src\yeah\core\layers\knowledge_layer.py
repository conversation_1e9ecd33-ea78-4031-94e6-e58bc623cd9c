"""
Knowledge Layer - Enriches intent with contextual understanding
Part of <PERSON><PERSON>'s Four-Layer Agentic Architecture
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import yaml

class KnowledgeContext:
    """Comprehensive context for Agent OS operations"""
    
    def __init__(self):
        self.product_context: Dict[str, Any] = {}
        self.project_history: List[Dict] = []
        self.team_conventions: Dict[str, Any] = {}
        self.steering_files: Dict[str, str] = {}
        self.standards: Dict[str, Any] = {}
        self.codebase_state: Dict[str, Any] = {}
        self.external_context: Dict[str, Any] = {}
        self.timestamp: datetime = datetime.now()

class KnowledgeLayer:
    """
    Knowledge Layer implementation following <PERSON><PERSON>'s principles:
    - Enriches intent with contextual understanding
    - Draws from local files, documentation, project history
    - Includes team conventions from steering files
    - Maintains awareness of project state and dependencies
    """
    
    def __init__(self, global_path: Path, project_path: Path):
        self.global_path = global_path
        self.project_path = project_path
        self.steering_path = project_path / "steering"
        self.cache: Dict[str, Any] = {}
        self.cache_timestamp: Optional[datetime] = None
    
    def enrich_intent(self, intent, context_requirements: List[str]) -> KnowledgeContext:
        """
        Enrich the parsed intent with comprehensive contextual knowledge
        """
        knowledge = KnowledgeContext()
        
        # Load core product context
        knowledge.product_context = self._load_product_context()
        
        # Load project history if required
        if 'git_history' in context_requirements:
            knowledge.project_history = self._load_project_history()
        
        # Load team conventions from steering files
        knowledge.team_conventions = self._load_team_conventions()
        
        # Load steering files content
        knowledge.steering_files = self._load_steering_files()
        
        # Load standards with project overrides
        knowledge.standards = self._load_standards()
        
        # Load codebase state if required
        if any(req in context_requirements for req in ['codebase_state', 'full_codebase']):
            knowledge.codebase_state = self._load_codebase_state(
                full_scan='full_codebase' in context_requirements
            )
        
        # Load external context (APIs, dependencies)
        knowledge.external_context = self._load_external_context()
        
        return knowledge
    
    def _load_product_context(self) -> Dict[str, Any]:
        """Load core product context files"""
        context = {}
        
        product_files = {
            'mission': 'mission.md',
            'tech_stack': 'tech-stack.md',
            'roadmap': 'roadmap.md',
            'decisions': 'decisions.md'
        }
        
        for key, filename in product_files.items():
            file_path = self.project_path / "product" / filename
            if file_path.exists():
                context[key] = self._parse_markdown_file(file_path)
        
        return context
    
    def _load_project_history(self) -> List[Dict]:
        """Load project history from git and decision logs"""
        history = []
        
        # Load from git if available
        try:
            import subprocess
            result = subprocess.run(
                ['git', 'log', '--oneline', '-20'],
                capture_output=True,
                text=True,
                cwd=self.project_path.parent
            )
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        commit_hash, message = line.split(' ', 1)
                        history.append({
                            'type': 'commit',
                            'hash': commit_hash,
                            'message': message,
                            'timestamp': None  # Could be enhanced with git show
                        })
        except Exception:
            pass  # Git not available or not a git repo
        
        # Load from decision logs
        decisions_file = self.project_path / "product" / "decisions.md"
        if decisions_file.exists():
            decisions_content = self._parse_markdown_file(decisions_file)
            # Extract decision entries (simplified parsing)
            history.append({
                'type': 'decisions',
                'content': decisions_content,
                'timestamp': datetime.now()
            })
        
        return history
    
    def _load_team_conventions(self) -> Dict[str, Any]:
        """Load team conventions from various sources"""
        conventions = {}
        
        # Load from steering files
        if self.steering_path.exists():
            for file_path in self.steering_path.glob("*.md"):
                key = file_path.stem
                conventions[key] = self._parse_markdown_file(file_path)
        
        # Load from project-specific standards
        standards_path = self.project_path / "product"
        for file_path in standards_path.glob("*-style.md"):
            key = file_path.stem.replace('-', '_')
            conventions[key] = self._parse_markdown_file(file_path)
        
        return conventions
    
    def _load_steering_files(self) -> Dict[str, str]:
        """Load steering files content for AI guidance"""
        steering_files = {}
        
        if self.steering_path.exists():
            for file_path in self.steering_path.glob("*.md"):
                steering_files[file_path.name] = file_path.read_text(encoding='utf-8')
        
        return steering_files
    
    def _load_standards(self) -> Dict[str, Any]:
        """Load standards with project overrides"""
        standards = {}
        
        # Load global standards
        global_standards_path = self.global_path / "standards"
        if global_standards_path.exists():
            for file_path in global_standards_path.glob("*.md"):
                key = file_path.stem.replace('-', '_')
                standards[key] = self._parse_markdown_file(file_path)
        
        # Apply project overrides
        project_standards_path = self.project_path / "product"
        if project_standards_path.exists():
            for file_path in project_standards_path.glob("*.md"):
                key = file_path.stem.replace('-', '_')
                if key in ['code_style', 'best_practices', 'tech_stack']:
                    standards[key] = self._parse_markdown_file(file_path)
        
        return standards
    
    def _load_codebase_state(self, full_scan: bool = False) -> Dict[str, Any]:
        """Load current codebase state and structure"""
        state = {
            'structure': {},
            'active_specs': [],
            'recent_changes': [],
            'dependencies': {}
        }
        
        # Load project structure
        project_root = self.project_path.parent
        if project_root.exists():
            state['structure'] = self._scan_directory_structure(project_root, full_scan)
        
        # Load active specs
        specs_path = self.project_path / "specs"
        if specs_path.exists():
            for spec_dir in specs_path.iterdir():
                if spec_dir.is_dir():
                    spec_info = self._load_spec_info(spec_dir)
                    if spec_info:
                        state['active_specs'].append(spec_info)
        
        # Load dependencies
        state['dependencies'] = self._load_dependencies(project_root)
        
        return state
    
    def _load_external_context(self) -> Dict[str, Any]:
        """Load external context like API documentation, dependencies"""
        external = {
            'apis': {},
            'libraries': {},
            'services': {}
        }
        
        # Load API documentation if available
        api_docs_path = self.project_path / "docs" / "apis"
        if api_docs_path.exists():
            for file_path in api_docs_path.glob("*.md"):
                external['apis'][file_path.stem] = self._parse_markdown_file(file_path)
        
        return external
    
    def _parse_markdown_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse markdown file with frontmatter support"""
        content = file_path.read_text(encoding='utf-8')
        
        # Extract YAML frontmatter
        frontmatter = {}
        body = content
        
        if content.startswith('---\n'):
            try:
                end_marker = content.find('\n---\n', 4)
                if end_marker != -1:
                    frontmatter_text = content[4:end_marker]
                    frontmatter = yaml.safe_load(frontmatter_text)
                    body = content[end_marker + 5:]
            except yaml.YAMLError:
                pass
        
        return {
            'frontmatter': frontmatter,
            'content': body,
            'path': str(file_path),
            'modified': datetime.fromtimestamp(file_path.stat().st_mtime)
        }
    
    def _scan_directory_structure(self, root_path: Path, full_scan: bool) -> Dict[str, Any]:
        """Scan directory structure with optional depth limit"""
        structure = {}
        
        try:
            max_depth = None if full_scan else 3
            for item in root_path.rglob("*"):
                if max_depth and len(item.parts) - len(root_path.parts) > max_depth:
                    continue
                
                relative_path = item.relative_to(root_path)
                if item.is_file():
                    structure[str(relative_path)] = {
                        'type': 'file',
                        'size': item.stat().st_size,
                        'modified': datetime.fromtimestamp(item.stat().st_mtime)
                    }
                elif item.is_dir():
                    structure[str(relative_path)] = {
                        'type': 'directory'
                    }
        except Exception:
            pass  # Handle permission errors gracefully
        
        return structure
    
    def _load_spec_info(self, spec_dir: Path) -> Optional[Dict[str, Any]]:
        """Load information about a specific spec"""
        srd_file = spec_dir / "srd.md"
        if not srd_file.exists():
            return None
        
        spec_info = {
            'name': spec_dir.name,
            'path': str(spec_dir),
            'srd': self._parse_markdown_file(srd_file)
        }
        
        # Load tasks if available
        tasks_file = spec_dir / "tasks.md"
        if tasks_file.exists():
            spec_info['tasks'] = self._parse_markdown_file(tasks_file)
        
        return spec_info
    
    def _load_dependencies(self, project_root: Path) -> Dict[str, Any]:
        """Load project dependencies from various manifest files"""
        dependencies = {}
        
        # Python dependencies
        requirements_file = project_root / "requirements.txt"
        if requirements_file.exists():
            dependencies['python'] = requirements_file.read_text().strip().split('\n')
        
        # Node.js dependencies
        package_json = project_root / "package.json"
        if package_json.exists():
            try:
                package_data = json.loads(package_json.read_text())
                dependencies['node'] = {
                    'dependencies': package_data.get('dependencies', {}),
                    'devDependencies': package_data.get('devDependencies', {})
                }
            except json.JSONDecodeError:
                pass
        
        return dependencies
