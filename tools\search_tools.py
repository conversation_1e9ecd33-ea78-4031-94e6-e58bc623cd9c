"""Code search tools for CLI Agent."""

import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from .base import Tool


class SearchCodeTool(Tool):
    """Tool for searching code in files."""
    
    @property
    def name(self) -> str:
        return "search_code"
    
    @property
    def description(self) -> str:
        return "Search for code patterns in files and return matching lines with context."
    
    async def execute(
        self,
        query: str,
        path: str,
        file_extensions: Optional[List[str]] = None,
        ignore_dirs: Optional[List[str]] = None,
        case_sensitive: bool = False,
        max_results: int = 50,
        context_lines: int = 2,
    ) -> Dict[str, Any]:
        """Search for code patterns in files.
        
        Args:
            query: Search query pattern.
            path: Base directory to search in.
            file_extensions: List of file extensions to search (e.g. ['.py', '.js']).
            ignore_dirs: List of directories to ignore.
            case_sensitive: Whether the search is case sensitive.
            max_results: Maximum number of results to return.
            context_lines: Number of context lines to include.
            
        Returns:
            Dictionary with search results.
        """
        try:
            # Default ignored directories
            default_ignore_dirs = {".git", "__pycache__", "node_modules", "venv", ".env"}
            
            # Combine with user-specified dirs
            if ignore_dirs:
                ignore_set = set(ignore_dirs) | default_ignore_dirs
            else:
                ignore_set = default_ignore_dirs
            
            # Normalize file extensions
            if file_extensions:
                exts = [ext if ext.startswith(".") else f".{ext}" for ext in file_extensions]
            else:
                # Default to common code files
                exts = [".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", ".json", ".md"]
            
            # Compile regex for search
            flags = 0 if case_sensitive else re.IGNORECASE
            try:
                regex = re.compile(query, flags)
            except re.error:
                # If query is not a valid regex, treat as literal string
                regex = re.compile(re.escape(query), flags)
            
            matches = []
            files_searched = 0
            
            # Walk directory and search files
            for root, dirs, files in os.walk(path):
                # Skip ignored directories
                dirs[:] = [d for d in dirs if d not in ignore_set]
                
                for filename in files:
                    file_path = os.path.join(root, filename)
                    
                    # Skip files with unwanted extensions
                    if not any(file_path.endswith(ext) for ext in exts):
                        continue
                    
                    files_searched += 1
                    
                    try:
                        with open(file_path, "r", encoding="utf-8", errors="replace") as f:
                            lines = f.readlines()
                        
                        # Search for matches
                        for i, line in enumerate(lines):
                            if regex.search(line):
                                # Get context lines
                                start = max(0, i - context_lines)
                                end = min(len(lines), i + context_lines + 1)
                                
                                context = []
                                for j in range(start, end):
                                    if j == i:
                                        # Mark the matching line
                                        context.append({
                                            "line_number": j + 1,
                                            "content": lines[j].rstrip(),
                                            "is_match": True,
                                        })
                                    else:
                                        context.append({
                                            "line_number": j + 1,
                                            "content": lines[j].rstrip(),
                                            "is_match": False,
                                        })
                                
                                # Add match to results
                                rel_path = os.path.relpath(file_path, path)
                                matches.append({
                                    "file": rel_path,
                                    "line": i + 1,
                                    "context": context,
                                })
                                
                                # Check if we've reached max results
                                if len(matches) >= max_results:
                                    break
                    
                    except Exception as e:
                        # Skip files that can't be read
                        continue
                    
                    # Check if we've reached max results
                    if len(matches) >= max_results:
                        break
                
                # Check if we've reached max results
                if len(matches) >= max_results:
                    break
            
            return {
                "query": query,
                "matches": matches,
                "count": len(matches),
                "files_searched": files_searched,
                "truncated": len(matches) >= max_results,
            }
        
        except Exception as e:
            return {"error": f"Error searching code: {str(e)}"}
