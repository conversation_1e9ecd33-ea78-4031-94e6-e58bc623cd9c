#!/usr/bin/env python3
"""
Yeah CLI - Agent OS-Native Coding Agent
Inspired by Claude Code and Agent OS workflows
"""

import click
import os
from pathlib import Path
from .core.agent_os_engine import Agent<PERSON>Engine, OperationMode

@click.group(invoke_without_command=True)
@click.option('--mode', type=click.Choice(['supervised', 'autopilot']), default='supervised',
              help='Operation mode: supervised (requires approval) or autopilot (autonomous)')
@click.option('--dry-run', is_flag=True, help='Show what would be done without executing')
@click.pass_context
def cli(ctx, mode, dry_run):
    """Yeah CLI - Agent OS-Native Coding Agent with <PERSON><PERSON>'s Four-Layer Architecture"""

    # Store options in context
    ctx.ensure_object(dict)
    ctx.obj['mode'] = OperationMode.AUTOPILOT if mode == 'autopilot' else OperationMode.SUPERVISED
    ctx.obj['dry_run'] = dry_run

    if ctx.invoked_subcommand is None:
        click.echo("Yeah CLI - Agent OS-Native Coding Agent")
        click.echo("Enhanced with <PERSON><PERSON>'s Four-Layer Agentic Architecture")
        click.echo("")
        click.echo("Commands:")
        click.echo("  /plan-product    - Initialize a new product with Agent OS structure")
        click.echo("  /create-spec     - Create a feature specification")
        click.echo("  /execute-tasks   - Execute tasks from specifications")
        click.echo("  /analyze-product - Analyze existing codebase")
        click.echo("  process          - Process any natural language request")
        click.echo("")
        click.echo("Options:")
        click.echo("  --mode [supervised|autopilot]  Operation mode")
        click.echo("  --dry-run                       Show actions without executing")

def _create_engine(ctx) -> AgentOSEngine:
    """Create and configure the Agent OS engine"""
    mode = ctx.obj.get('mode', OperationMode.SUPERVISED)
    engine = AgentOSEngine(mode=mode)

    if ctx.obj.get('dry_run', False):
        engine.enable_dry_run(True)

    return engine

def _approval_callback(plan, oversight_report):
    """Interactive approval callback for supervised mode"""
    click.echo("\n" + "="*60)
    click.echo("EXECUTION PLAN REVIEW")
    click.echo("="*60)

    # Show oversight report
    click.echo(f"\nCompliance Score: {oversight_report.compliance_score:.2f}")
    click.echo(f"Risk Level: {oversight_report.risk_assessment.get('level', 'unknown')}")

    if oversight_report.validations:
        click.echo("\nValidation Results:")
        for validation in oversight_report.validations:
            status = "✓" if validation.passed else "✗"
            click.echo(f"  {status} {validation.validator_name}: {validation.message}")

    if oversight_report.recommendations:
        click.echo("\nRecommendations:")
        for rec in oversight_report.recommendations:
            click.echo(f"  • {rec}")

    # Show planned actions
    click.echo(f"\nPlanned Actions ({len(plan.actions)}):")
    for i, action in enumerate(plan.actions, 1):
        click.echo(f"  {i}. {action['type']}: {action.get('description', 'No description')}")

    click.echo(f"\nEstimated Duration: {plan.estimated_duration} minutes")
    click.echo(f"Risk Level: {plan.risk_level}")

    return click.confirm("\nDo you approve this execution plan?")

@cli.command(name="plan-product")
@click.pass_context
def plan_product(ctx):
    """Initialize a new product with Agent OS structure"""
    engine = _create_engine(ctx)

    result = engine.process_request("/plan-product", _approval_callback)

    if result.get('error'):
        click.echo(f"Error: {result['error']}", err=True)
        return

    if result.get('executed'):
        click.echo("✓ Product initialization completed successfully!")
    elif result.get('needs_approval'):
        click.echo("⚠ Execution plan requires approval but was not approved.")
    else:
        click.echo("ℹ Execution plan created but not executed.")

@cli.command(name="create-spec")
@click.argument('feature_name', required=False)
@click.pass_context
def create_spec(ctx, feature_name):
    """Create a feature specification"""
    engine = _create_engine(ctx)

    command = "/create-spec"
    if feature_name:
        command += f" for {feature_name}"

    result = engine.process_request(command, _approval_callback)

    if result.get('error'):
        click.echo(f"Error: {result['error']}", err=True)
        return

    if result.get('executed'):
        click.echo("✓ Feature specification created successfully!")
    elif result.get('needs_approval'):
        click.echo("⚠ Execution plan requires approval but was not approved.")
    else:
        click.echo("ℹ Execution plan created but not executed.")

@cli.command(name="execute-tasks")
@click.argument('task_id', required=False)
@click.pass_context
def execute_tasks(ctx, task_id):
    """Execute tasks from specifications"""
    engine = _create_engine(ctx)

    command = "/execute-tasks"
    if task_id:
        command += f" task {task_id}"

    result = engine.process_request(command, _approval_callback)

    if result.get('error'):
        click.echo(f"Error: {result['error']}", err=True)
        return

    if result.get('executed'):
        click.echo("✓ Tasks executed successfully!")
    elif result.get('needs_approval'):
        click.echo("⚠ Execution plan requires approval but was not approved.")
    else:
        click.echo("ℹ Execution plan created but not executed.")

@cli.command(name="analyze-product")
@click.pass_context
def analyze_product(ctx):
    """Analyze existing codebase and add Agent OS structure"""
    engine = _create_engine(ctx)

    result = engine.process_request("/analyze-product", _approval_callback)

    if result.get('error'):
        click.echo(f"Error: {result['error']}", err=True)
        return

    if result.get('executed'):
        click.echo("✓ Product analysis completed successfully!")
    elif result.get('needs_approval'):
        click.echo("⚠ Execution plan requires approval but was not approved.")
    else:
        click.echo("ℹ Execution plan created but not executed.")

@cli.command(name="process")
@click.argument('request', required=True)
@click.pass_context
def process_request(ctx, request):
    """Process any natural language request using Kiro's architecture"""
    engine = _create_engine(ctx)

    result = engine.process_request(request, _approval_callback)

    if result.get('error'):
        click.echo(f"Error: {result['error']}", err=True)
        return

    # Show intent analysis
    intent = result.get('intent')
    if intent:
        click.echo(f"\nIntent Analysis:")
        click.echo(f"  Type: {intent.intent_type.value}")
        click.echo(f"  Category: {intent.category.value}")
        click.echo(f"  Confidence: {intent.confidence:.2f}")

    if result.get('executed'):
        click.echo("\n✓ Request processed and executed successfully!")

        # Show execution results
        execution_results = result.get('execution_results', [])
        if execution_results:
            click.echo(f"\nExecution Results ({len(execution_results)} actions):")
            for i, exec_result in enumerate(execution_results, 1):
                status = "✓" if exec_result.success else "✗"
                click.echo(f"  {i}. {status} {exec_result.action_type.value}: {exec_result.output}")
    elif result.get('needs_approval'):
        click.echo("\n⚠ Request requires approval but was not approved.")
    else:
        click.echo("\nℹ Request analyzed but not executed.")

if __name__ == "__main__":
    cli()