#!/usr/bin/env python3
"""
Yeah CLI - Agent OS-Native Coding Agent
Inspired by Claude Code and Agent OS workflows
"""

import click
import os
from pathlib import Path
from .core.agent_os_engine import AgentOSEngine
from .commands import PlanProductCommand, CreateSpecCommand, ExecuteTasksCommand, AnalyzeProductCommand

@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """Yeah CLI - Agent OS-Native Coding Agent"""
    if ctx.invoked_subcommand is None:
        click.echo("Yeah CLI - Agent OS-Native Coding Agent")
        click.echo("Use /plan-product, /create-spec, /execute-tasks, or /analyze-product")

@cli.command(name="plan-product")
@click.pass_context
def plan_product(ctx):
    """Execute Agent OS plan-product workflow"""
    engine = AgentOSEngine()
    command = PlanProductCommand(engine)
    command.execute()

@cli.command(name="create-spec")
@click.pass_context
def create_spec(ctx):
    """Execute Agent OS create-spec workflow"""
    engine = AgentOSEngine()
    command = CreateSpecCommand(engine)
    command.execute()

@cli.command(name="execute-tasks")
@click.pass_context
def execute_tasks(ctx):
    """Execute Agent OS execute-tasks workflow"""
    engine = AgentOSEngine()
    command = ExecuteTasksCommand(engine)
    command.execute()

@cli.command(name="analyze-product")
@click.pass_context
def analyze_product(ctx):
    """Execute Agent OS analyze-product workflow"""
    engine = AgentOSEngine()
    command = AnalyzeProductCommand(engine)
    command.execute()

if __name__ == "__main__":
    cli()