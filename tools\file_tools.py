"""File operation tools for CLI Agent."""

import os
import glob
from pathlib import Path
from typing import Any, Dict, List, Optional

from .base import Tool


class ReadFileTool(Tool):
    """Tool for reading file contents."""
    
    @property
    def name(self) -> str:
        return "read_file"
    
    @property
    def description(self) -> str:
        return "Read contents of a file at the specified path."
    
    async def execute(self, path: str, max_tokens: Optional[int] = None) -> Dict[str, Any]:
        """Read file contents.
        
        Args:
            path: Path to the file.
            max_tokens: Maximum number of tokens to read (approximate, based on characters).
            
        Returns:
            Dictionary with file contents and metadata.
        """
        try:
            file_path = Path(path)
            
            if not file_path.exists():
                return {"error": f"File not found: {path}"}
            
            if not file_path.is_file():
                return {"error": f"Not a file: {path}"}
            
            with open(file_path, "r", encoding="utf-8", errors="replace") as f:
                content = f.read()
            
            # Truncate content if max_tokens is specified (rough approximation)
            if max_tokens and len(content) > max_tokens * 4:  # Rough char-to-token ratio
                content = content[:max_tokens * 4] + "\n... [content truncated]"
            
            return {
                "content": content,
                "path": str(file_path),
                "size": file_path.stat().st_size,
                "modified": file_path.stat().st_mtime,
            }
        
        except Exception as e:
            return {"error": f"Error reading file: {str(e)}"}


class WriteFileTool(Tool):
    """Tool for writing to files."""
    
    @property
    def name(self) -> str:
        return "write_file"
    
    @property
    def description(self) -> str:
        return "Write content to a file at the specified path."
    
    async def execute(
        self, path: str, content: str, append: bool = False
    ) -> Dict[str, Any]:
        """Write content to a file.
        
        Args:
            path: Path to the file.
            content: Content to write.
            append: Whether to append to existing content.
            
        Returns:
            Dictionary with operation result.
        """
        try:
            file_path = Path(path)
            
            # Create parent directories if they don't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            mode = "a" if append else "w"
            with open(file_path, mode, encoding="utf-8") as f:
                f.write(content)
            
            return {
                "success": True,
                "path": str(file_path),
                "size": file_path.stat().st_size,
                "modified": file_path.stat().st_mtime,
                "action": "append" if append else "write",
            }
        
        except Exception as e:
            return {"error": f"Error writing to file: {str(e)}"}


class ListFilesTool(Tool):
    """Tool for listing files and directories."""
    
    @property
    def name(self) -> str:
        return "list_files"
    
    @property
    def description(self) -> str:
        return "List files and directories at the specified path with optional pattern filtering."
    
    async def execute(
        self,
        path: str,
        pattern: Optional[str] = None,
        recursive: bool = False,
        include_hidden: bool = False,
        max_items: int = 100,
    ) -> Dict[str, Any]:
        """List files and directories.
        
        Args:
            path: Directory path.
            pattern: Optional glob pattern for filtering.
            recursive: Whether to list recursively.
            include_hidden: Whether to include hidden files.
            max_items: Maximum number of items to return.
            
        Returns:
            Dictionary with list of files and directories.
        """
        try:
            base_path = Path(path)
            
            if not base_path.exists():
                return {"error": f"Path not found: {path}"}
            
            if not base_path.is_dir():
                return {"error": f"Not a directory: {path}"}
            
            # Prepare glob pattern
            if pattern:
                if recursive:
                    glob_pattern = f"{path}/**/{pattern}"
                else:
                    glob_pattern = f"{path}/{pattern}"
            else:
                if recursive:
                    glob_pattern = f"{path}/**/*"
                else:
                    glob_pattern = f"{path}/*"
            
            # Get files and directories
            files = []
            dirs = []
            
            items = list(glob.glob(glob_pattern, recursive=recursive))
            
            for item_path in items:
                # Skip hidden files/dirs if not included
                if not include_hidden and os.path.basename(item_path).startswith("."):
                    continue
                
                item = Path(item_path)
                item_info = {
                    "path": str(item),
                    "name": item.name,
                    "modified": item.stat().st_mtime,
                }
                
                if item.is_file():
                    item_info["type"] = "file"
                    item_info["size"] = item.stat().st_size
                    files.append(item_info)
                elif item.is_dir():
                    item_info["type"] = "directory"
                    dirs.append(item_info)
                
                # Check if we've reached the limit
                if len(files) + len(dirs) >= max_items:
                    break
            
            return {
                "path": str(base_path),
                "files": files,
                "directories": dirs,
                "count": len(files) + len(dirs),
                "truncated": len(files) + len(dirs) >= max_items,
            }
        
        except Exception as e:
            return {"error": f"Error listing files: {str(e)}"}
