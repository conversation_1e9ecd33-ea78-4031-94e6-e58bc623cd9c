"""Context management for CLI Agent."""

import glob
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

import tiktoken

from .config import Config


class ContextManager:
    """Manager for gathering and organizing context for the agent."""
    
    def __init__(self, config: Config):
        """Initialize the context manager.
        
        Args:
            config: Agent configuration.
        """
        self.config = config
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        # File extensions to prioritize for context
        self.code_extensions = {
            ".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", 
            ".c", ".cpp", ".h", ".hpp", ".java", ".go", ".rs",
        }
        
        # Documentation file extensions
        self.doc_extensions = {
            ".md", ".txt", ".rst", ".json", ".yaml", ".yml", ".toml",
        }
        
        # Files to ignore
        self.ignore_patterns = [
            r"__pycache__/", r"\.git/", r"\.env", r"\.venv", r"node_modules/",
            r"\.pyc$", r"\.pyo$", r"\.pyd$", r"\.so$", r"\.dll$", r"\.exe$",
            r"\.bin$", r"\.obj$", r"\.swp$", r"~$", r"\.bak$", r"\.tmp$",
            r"\.log$", r"\.class$", r"\.jar$", r"\.db$", r"\.sqlite$",
        ]
        
        # Compiled regex patterns
        self.ignore_regex = re.compile("|".join(self.ignore_patterns))
        
        # Maximum tokens for context
        self.max_context_tokens = 16000  # Default, adjust based on model
    
    def get_context(
        self, task: str, project_path: str, step_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Get context for a task.
        
        Args:
            task: Task description.
            project_path: Path to project directory.
            step_results: Previous step results.
            
        Returns:
            Dictionary with context information.
        """
        context = {
            "files": [],
            "project_structure": "",
            "current_directory": os.path.abspath(os.getcwd()),
            "project_path": os.path.abspath(project_path),
        }
        
        # Get project structure
        context["project_structure"] = self._get_project_structure(project_path)
        
        # Extract keywords from task
        keywords = self._extract_keywords(task, step_results)
        
        # Find relevant files
        relevant_files = self._find_relevant_files(project_path, keywords)
        
        # Read and process files with token limits
        context["files"] = self._process_files_with_token_limit(relevant_files)
        
        return context
    
    def _extract_keywords(
        self, task: str, step_results: List[Dict[str, Any]]
    ) -> Set[str]:
        """Extract keywords from task and step results.
        
        Args:
            task: Task description.
            step_results: Previous step results.
            
        Returns:
            Set of keywords.
        """
        keywords = set()
        
        # Extract words that might be file names or programming terms
        # This is a simple extraction, a real implementation might use NLP
        words = re.findall(r'\b\w+\b', task.lower())
        keywords.update(words)
        
        # Extract file extensions mentioned in the task
        extensions = re.findall(r'\.\w+\b', task.lower())
        keywords.update(extensions)
        
        # Extract file paths mentioned in the task
        paths = re.findall(r'\b[\w\./\\-]+\.\w+\b', task)
        for path in paths:
            keywords.add(path)
            keywords.add(os.path.basename(path))
        
        # Extract keywords from step results
        for step in step_results:
            if "reasoning" in step:
                words = re.findall(r'\b\w+\b', step["reasoning"].lower())
                keywords.update(words)
            
            if "action" in step and step["action"] in ["read_file", "write_file"]:
                if "action_input" in step and "path" in step["action_input"]:
                    path = step["action_input"]["path"]
                    keywords.add(path)
                    keywords.add(os.path.basename(path))
        
        # Filter out common words and short words
        common_words = {
            "the", "and", "or", "of", "to", "a", "in", "that", "is", "for",
            "from", "on", "by", "with", "as", "at", "an", "be", "this", "which",
        }
        keywords = {k for k in keywords if len(k) > 2 and k not in common_words}
        
        return keywords
    
    def _find_relevant_files(self, project_path: str, keywords: Set[str]) -> List[str]:
        """Find files relevant to the task.
        
        Args:
            project_path: Path to project directory.
            keywords: Keywords to match.
            
        Returns:
            List of relevant file paths.
        """
        all_files = []
        
        # Walk through the project directory
        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not self.ignore_regex.search(d)]
            
            for file in files:
                # Skip ignored files
                if self.ignore_regex.search(file):
                    continue
                
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_path)
                
                # Get file extension
                _, ext = os.path.splitext(file)
                
                # Score file relevance
                score = self._score_file_relevance(file, rel_path, ext, keywords)
                
                if score > 0:
                    all_files.append((file_path, score))
        
        # Sort by relevance score (descending)
        all_files.sort(key=lambda x: x[1], reverse=True)
        
        # Get just the file paths
        return [f[0] for f in all_files]
    
    def _score_file_relevance(
        self, file_name: str, rel_path: str, extension: str, keywords: Set[str]
    ) -> float:
        """Score file relevance based on name, path, and keywords.
        
        Args:
            file_name: File name.
            rel_path: Relative file path.
            extension: File extension.
            keywords: Keywords to match.
            
        Returns:
            Relevance score.
        """
        score = 0.0
        
        # Base score by file type
        if extension in self.code_extensions:
            score += 2.0
        elif extension in self.doc_extensions:
            score += 1.0
        else:
            score += 0.1
        
        # Match keywords
        for keyword in keywords:
            # Exact file name match is strong
            if keyword.lower() == file_name.lower():
                score += 10.0
            # File name contains keyword
            elif keyword.lower() in file_name.lower():
                score += 5.0
            # Path contains keyword
            elif keyword.lower() in rel_path.lower():
                score += 2.0
        
        # Prioritize top-level files
        depth = rel_path.count(os.sep)
        score += 1.0 / (depth + 1)
        
        # Add special files
        special_files = ["readme", "config", "setup", "main", "index", "app"]
        base_name = os.path.splitext(file_name)[0].lower()
        if base_name in special_files:
            score += 3.0
        
        return score
    
    def _process_files_with_token_limit(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """Process files while respecting token limits.
        
        Args:
            file_paths: Paths of files to process.
            
        Returns:
            List of processed file contents.
        """
        processed_files = []
        total_tokens = 0
        
        for file_path in file_paths:
            try:
                # Skip large binary files and very large text files
                file_size = os.path.getsize(file_path)
                if file_size > 1_000_000:  # 1MB
                    continue
                
                # Read file content
                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                
                # Count tokens
                tokens = len(self.encoding.encode(content))
                
                # Skip if this file would exceed the token limit
                if total_tokens + tokens > self.max_context_tokens:
                    # Try to include a truncated version instead
                    max_remaining_tokens = self.max_context_tokens - total_tokens
                    if max_remaining_tokens > 500:  # Ensure we have enough space for a meaningful chunk
                        # Truncate content to fit the limit
                        truncate_ratio = max_remaining_tokens / tokens
                        content_chars = int(len(content) * truncate_ratio) - 100
                        content = content[:content_chars] + "\n...\n[Content truncated due to token limit]"
                        tokens = len(self.encoding.encode(content))
                    else:
                        # Not enough space for a meaningful chunk
                        continue
                
                # Add to processed files
                processed_files.append({
                    "path": file_path,
                    "content": content,
                    "tokens": tokens,
                    "size": file_size,
                })
                
                total_tokens += tokens
                
                # Stop if we've reached the token limit
                if total_tokens >= self.max_context_tokens:
                    break
            
            except Exception as e:
                # Skip files that can't be read
                continue
        
        return processed_files
    
    def _get_project_structure(self, project_path: str, max_depth: int = 3) -> str:
        """Get a string representation of the project directory structure.
        
        Args:
            project_path: Path to project directory.
            max_depth: Maximum directory depth to display.
            
        Returns:
            String representation of project structure.
        """
        if not os.path.exists(project_path):
            return f"Project path not found: {project_path}"
        
        result = []
        
        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not self.ignore_regex.search(d)]
            
            # Check depth
            rel_path = os.path.relpath(root, project_path)
            depth = 0 if rel_path == "." else rel_path.count(os.sep) + 1
            
            if depth > max_depth:
                # Indicate there are more subdirectories but don't recurse
                dirs[:] = []
                continue
            
            # Add directory
            if depth == 0:
                result.append(os.path.basename(project_path) + "/")
            else:
                indent = "  " * depth
                result.append(f"{indent}{os.path.basename(root)}/")
            
            # Add files (limit to 10 files per directory to avoid clutter)
            files = [f for f in files if not self.ignore_regex.search(f)]
            if files:
                indent = "  " * (depth + 1)
                if len(files) <= 10:
                    for file in files:
                        result.append(f"{indent}{file}")
                else:
                    for file in files[:7]:
                        result.append(f"{indent}{file}")
                    result.append(f"{indent}... ({len(files) - 7} more files)")
        
        return "\n".join(result)
