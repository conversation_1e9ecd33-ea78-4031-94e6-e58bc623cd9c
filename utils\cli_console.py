"""CLI Console utility for CLI Agent."""

import os
import time
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.syntax import Syntax
from rich.table import Table

from .config import Config

console = Console()


class CLIConsole:
    """Console output manager for CLI Agent."""
    
    def __init__(self, config: Config):
        """Initialize CLI console.
        
        Args:
            config: Agent configuration.
        """
        self.config = config
        self.spinner = None
        self.step_progress = None
    
    def print_task_details(
        self,
        task: str,
        working_dir: str,
        provider: str,
        model: str,
        max_steps: int,
        config_file: str,
        trajectory_path: str,
    ) -> None:
        """Print task details.
        
        Args:
            task: Task description.
            working_dir: Working directory path.
            provider: LLM provider name.
            model: Model name.
            max_steps: Maximum number of steps.
            config_file: Configuration file path.
            trajectory_path: Trajectory file path.
        """
        console.print(
            Panel(
                f"[bold white]Task:[/bold white] {task}\n\n"
                f"[bold blue]Working Directory:[/bold blue] {working_dir}\n"
                f"[bold blue]Provider:[/bold blue] {provider}\n"
                f"[bold blue]Model:[/bold blue] {model}\n"
                f"[bold blue]Max Steps:[/bold blue] {max_steps}\n"
                f"[bold blue]Config File:[/bold blue] {config_file}\n"
                f"[bold blue]Trajectory Path:[/bold blue] {trajectory_path}",
                title="CLI Agent Task",
                border_style="green",
            )
        )
    
    def start_progress(self, total_steps: int) -> None:
        """Start progress display.
        
        Args:
            total_steps: Total number of steps.
        """
        if not self.step_progress:
            self.step_progress = Progress(
                SpinnerColumn(),
                TextColumn("[bold blue]{task.description}"),
                TextColumn("[bold cyan]{task.fields[step]}"),
                TimeElapsedColumn(),
                console=console,
            )
            self.step_progress.start()
            self.step_progress_task = self.step_progress.add_task(
                "Executing task", total=total_steps, step=""
            )
    
    def update_progress(self, step: int, description: str) -> None:
        """Update progress display.
        
        Args:
            step: Current step number.
            description: Step description.
        """
        if self.step_progress:
            self.step_progress.update(
                self.step_progress_task, completed=step, step=description
            )
    
    def stop_progress(self) -> None:
        """Stop progress display."""
        if self.step_progress:
            self.step_progress.stop()
            self.step_progress = None
    
    def display_step(self, step_idx: int, step_result: Dict[str, Any]) -> None:
        """Display step information.
        
        Args:
            step_idx: Step index.
            step_result: Step result data.
        """
        # Clear any progress indicators
        if self.step_progress:
            self.step_progress.stop()
            self.step_progress = None
        
        # Create step panel
        step_panel_content = []
        
        # Add reasoning if available
        if "reasoning" in step_result and step_result["reasoning"]:
            step_panel_content.append("[bold white]Reasoning:[/bold white]")
            step_panel_content.append(step_result["reasoning"])
            step_panel_content.append("")
        
        # Add action if available
        if "action" in step_result and step_result["action"]:
            step_panel_content.append(
                f"[bold green]Action:[/bold green] {step_result['action']}"
            )
            
            # Add action input if available
            if "action_input" in step_result and step_result["action_input"]:
                step_panel_content.append("[bold yellow]Input:[/bold yellow]")
                step_panel_content.append(
                    Syntax(
                        str(step_result["action_input"]),
                        "json",
                        theme="monokai",
                        background_color="default",
                    )
                )
            
            # Add action output if available
            if "action_output" in step_result and step_result["action_output"]:
                step_panel_content.append("[bold blue]Output:[/bold blue]")
                
                # Format output based on content
                output = step_result["action_output"]
                if isinstance(output, dict) and "content" in output:
                    # For file content, show file info and truncate content if needed
                    if "path" in output:
                        step_panel_content.append(f"[bold]File:[/bold] {output['path']}")
                    
                    content = output["content"]
                    if len(content) > 1000:
                        content = content[:1000] + "...\n[Content truncated]"
                    
                    step_panel_content.append(
                        Syntax(
                            content,
                            "python",  # Default syntax, would be better to detect
                            theme="monokai",
                            background_color="default",
                            word_wrap=True,
                        )
                    )
                else:
                    # For other outputs, show as JSON or string
                    try:
                        step_panel_content.append(
                            Syntax(
                                str(output),
                                "json" if isinstance(output, dict) else "text",
                                theme="monokai",
                                background_color="default",
                                word_wrap=True,
                            )
                        )
                    except Exception:
                        step_panel_content.append(str(output))
        
        # Add error if available
        if "error" in step_result:
            step_panel_content.append(f"[bold red]Error:[/bold red] {step_result['error']}")
        
        # Display the step panel
        console.print(
            Panel(
                "\n".join(str(content) for content in step_panel_content),
                title=f"Step {step_idx + 1}",
                border_style="blue",
            )
        )
    
    def display_summary(self, steps_taken: int, summary: str) -> None:
        """Display task summary.
        
        Args:
            steps_taken: Number of steps taken.
            summary: Task summary.
        """
        console.print(
            Panel(
                f"[bold white]Steps taken:[/bold white] {steps_taken}\n\n{summary}",
                title="Task Summary",
                border_style="green",
            )
        )
    
    def print_error(self, error_message: str) -> None:
        """Print error message.
        
        Args:
            error_message: Error message.
        """
        console.print(
            Panel(
                error_message,
                title="Error",
                border_style="red",
            )
        )
    
    def print_warning(self, warning_message: str) -> None:
        """Print warning message.
        
        Args:
            warning_message: Warning message.
        """
        console.print(
            Panel(
                warning_message,
                title="Warning",
                border_style="yellow",
            )
        )
    
    def print_info(self, info_message: str) -> None:
        """Print info message.
        
        Args:
            info_message: Info message.
        """
        console.print(
            Panel(
                info_message,
                title="Info",
                border_style="blue",
            )
        )
    
    def print_success(self, success_message: str) -> None:
        """Print success message.
        
        Args:
            success_message: Success message.
        """
        console.print(
            Panel(
                success_message,
                title="Success",
                border_style="green",
            )
        )
