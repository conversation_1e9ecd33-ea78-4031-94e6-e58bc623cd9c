"""
Markdown Processor - Parses Agent OS instruction files
"""

import re
from pathlib import Path
from typing import Dict, List, Any
import yaml

class MarkdownProcessor:
    """Processes Agent OS markdown instruction files"""
    
    def parse_instruction_file(self, file_path: Path) -> Dict:
        """Parse Agent OS instruction file into executable structure"""
        content = file_path.read_text()
        
        # Extract YAML frontmatter
        frontmatter = self._extract_frontmatter(content)
        
        # Extract structured blocks
        ai_meta = self._extract_xml_block(content, "ai_meta")
        purpose = self._extract_xml_block(content, "purpose")
        steps = self._extract_workflow_steps(content)
        templates = self._extract_templates(content)
        instructions = self._extract_instruction_blocks(content)
        
        return {
            "frontmatter": frontmatter,
            "ai_meta": ai_meta,
            "purpose": purpose,
            "steps": steps,
            "templates": templates,
            "instructions": instructions,
            "raw_content": content
        }
    
    def _extract_frontmatter(self, content: str) -> Dict:
        """Extract YAML frontmatter from markdown"""
        match = re.match(r'^---\n(.*?)\n---', content, re.DOTALL)
        if match:
            try:
                return yaml.safe_load(match.group(1))
            except yaml.YAMLError:
                return {}
        return {}
    
    def _extract_xml_block(self, content: str, tag: str) -> Dict:
        """Extract XML block content"""
        pattern = f'<{tag}>(.*?)</{tag}>'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            return {"content": match.group(1).strip()}
        return {}
    
    def _extract_workflow_steps(self, content: str) -> List[Dict]:
        """Extract workflow steps from instruction file"""
        steps = []
        step_pattern = r'<step number="(\d+)" name="([^"]+)">(.*?)(?=<step|$)'
        
        for match in re.finditer(step_pattern, content, re.DOTALL):
            step_number = int(match.group(1))
            step_name = match.group(2)
            step_content = match.group(3).strip()
            
            steps.append({
                "number": step_number,
                "name": step_name,
                "content": step_content,
                "instructions": self._extract_instruction_blocks(step_content)
            })
        
        return steps
    
    def _extract_templates(self, content: str) -> Dict[str, str]:
        """Extract content templates"""
        templates = {}
        template_pattern = r'<([^>]+_template)>(.*?)</\1>'
        
        for match in re.finditer(template_pattern, content, re.DOTALL):
            template_name = match.group(1)
            template_content = match.group(2).strip()
            templates[template_name] = template_content
        
        return templates
    
    def _extract_instruction_blocks(self, content: str) -> List[Dict]:
        """Extract instruction blocks"""
        instructions = []
        instruction_pattern = r'<instructions>(.*?)</instructions>'
        
        for match in re.finditer(instruction_pattern, content, re.DOTALL):
            instruction_content = match.group(1).strip()
            instructions.append({
                "content": instruction_content,
                "actions": self._parse_instruction_actions(instruction_content)
            })
        
        return instructions
    
    def _parse_instruction_actions(self, content: str) -> List[Dict]:
        """Parse individual actions from instruction content"""
        actions = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if ':' in line and line.isupper():
                action_type, action_content = line.split(':', 1)
                actions.append({
                    "type": action_type.strip(),
                    "content": action_content.strip()
                })
        
        return actions