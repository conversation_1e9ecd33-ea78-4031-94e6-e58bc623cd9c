"""Base tool classes for CLI Agent."""

import abc
import asyncio
import inspect
import logging
from typing import Any, Dict, List, Optional, Type

logger = logging.getLogger(__name__)


class Tool(abc.ABC):
    """Base class for all tools."""
    
    @property
    @abc.abstractmethod
    def name(self) -> str:
        """The name of the tool."""
        pass
    
    @property
    @abc.abstractmethod
    def description(self) -> str:
        """Description of what the tool does."""
        pass
    
    @property
    def parameters(self) -> Dict[str, Dict[str, Any]]:
        """Parameters for the tool.
        
        Default implementation extracts parameters from the execute method signature.
        Override this method for more complex parameter definitions.
        
        Returns:
            Dictionary mapping parameter names to their specifications.
        """
        params = {}
        signature = inspect.signature(self.execute)
        
        # Skip 'self' parameter
        for param_name, param in list(signature.parameters.items())[1:]:
            param_info = {"type": "string"}
            
            if param.annotation != inspect.Parameter.empty:
                if param.annotation == int:
                    param_info["type"] = "integer"
                elif param.annotation == bool:
                    param_info["type"] = "boolean"
                elif param.annotation == float:
                    param_info["type"] = "number"
                elif param.annotation == list or param.annotation == List:
                    param_info["type"] = "array"
            
            if param.default != inspect.Parameter.empty and param.default is not None:
                param_info["default"] = param.default
            
            params[param_name] = param_info
        
        return params
    
    @abc.abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with the given arguments.
        
        Args:
            **kwargs: Tool-specific arguments.
            
        Returns:
            Tool execution results.
        """
        pass


class ToolExecutor:
    """Tool execution engine."""
    
    def __init__(self):
        """Initialize the tool executor."""
        self.tools = {}
    
    def register_tool(self, tool: Tool) -> None:
        """Register a tool with the executor.
        
        Args:
            tool: Tool instance to register.
        """
        self.tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name}")
    
    def get_tools_schema(self) -> List[Dict[str, Any]]:
        """Get schema for all registered tools.
        
        Returns:
            List of tool schema dictionaries.
        """
        schemas = []
        for name, tool in self.tools.items():
            schemas.append({
                "name": name,
                "description": tool.description,
                "parameters": {
                    "type": "object",
                    "properties": tool.parameters,
                },
            })
        return schemas
    
    async def execute_tool(self, tool_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name with the given arguments.
        
        Args:
            tool_name: Name of the tool to execute.
            args: Tool arguments.
            
        Returns:
            Tool execution results.
            
        Raises:
            ValueError: If the tool is not found.
        """
        if tool_name not in self.tools:
            raise ValueError(f"Tool not found: {tool_name}")
        
        tool = self.tools[tool_name]
        try:
            logger.info(f"Executing tool: {tool_name}")
            logger.debug(f"Tool args: {args}")
            
            # Execute the tool
            result = await tool.execute(**args)
            
            logger.info(f"Tool execution complete: {tool_name}")
            logger.debug(f"Tool result: {result}")
            
            return result
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            raise
