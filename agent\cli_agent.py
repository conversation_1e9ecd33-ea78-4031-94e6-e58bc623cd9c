"""CLI Agent implementation."""

import asyncio
import json
import logging
import os
import re
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel

from cli_agent.agent.base import Agent
from cli_agent.tools.base import Tool, ToolExecutor
from cli_agent.tools import get_default_tools
from cli_agent.utils.context_manager import ContextManager
from cli_agent.utils.config import Config
from cli_agent.utils.memory import Memory

console = Console()


class CliAgent(Agent):
    """CLI Agent for software engineering tasks."""

    def __init__(self, config: Config):
        """Initialize the CLI Agent.
        
        Args:
            config: Agent configuration.
        """
        super().__init__(config)
        
        # Initialize tools
        self.tool_executor = ToolExecutor()
        self.tools = get_default_tools()
        
        # Register all tools with the executor
        for tool_name, tool_cls in self.tools.items():
            self.tool_executor.register_tool(tool_cls())
        
        # Initialize context manager
        self.context_manager = ContextManager(config)
        
        # Initialize memory
        self.memory = Memory(config)
        
        # Step tracking
        self.last_prompt = None
        self.last_response = None
    
    async def execute_step(self, step_idx: int) -> Tuple[bool, Dict[str, Any]]:
        """Execute a single step in the agent's reasoning process.
        
        Args:
            step_idx: Current step index.
            
        Returns:
            Tuple of (is_done, step_result)
        """
        step_start_time = time.time()
        is_done = False
        
        try:
            # Gather relevant context for the task
            context = self.context_manager.get_context(
                self.task, 
                self.task_args.get("project_path", os.getcwd()),
                self.step_results
            )
            
            # Get relevant memories
            memories = self.memory.get_relevant_memories(self.task)
            if memories:
                context["memories"] = memories
            
            # Build prompt for this step
            prompt = self._build_prompt(step_idx, context)
            self.last_prompt = prompt
            
            # Send prompt to LLM
            response = await self.llm_client.generate(prompt)
            self.last_response = response
            
            # Parse response to extract reasoning, action, and is_done
            reasoning, action, action_input, is_done = self._parse_response(response)
            
            # Execute action if provided
            action_output = None
            if action:
                action_output = await self._execute_action(action, action_input)
            
            # Record step result
            step_result = {
                "step_idx": step_idx,
                "reasoning": reasoning,
                "action": action,
                "action_input": action_input,
                "action_output": action_output,
                "is_done": is_done,
                "timestamp": time.time(),
                "duration": time.time() - step_start_time,
            }
            
            # Update trajectory
            self.trajectory.append(step_result)
            self._save_trajectory()
            
            # Display step progress
            if self.console:
                self.console.display_step(step_idx, step_result)
            
            return is_done, step_result
        
        except Exception as e:
            error_msg = f"Step {step_idx} error: {str(e)}"
            step_result = {
                "step_idx": step_idx,
                "error": error_msg,
                "traceback": traceback.format_exc(),
                "timestamp": time.time(),
                "duration": time.time() - step_start_time,
            }
            
            # Log error
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            # Display error
            if self.console:
                self.console.print_error(error_msg)
            
            return is_done, step_result
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the full task until completion or max steps.
        
        Returns:
            Task results including all steps taken.
        """
        task_start_time = time.time()
        
        try:
            # Record task execution start
            self._record_event("task_execution_start", {
                "task_id": self.task_id,
                "task": self.task,
            })
            
            # Initialize memory for this task
            self.memory.init_task_memory(self.task_id)
            
            for step_idx in range(self.max_steps):
                self.steps_taken = step_idx + 1
                
                # Execute step
                is_done, step_result = await self.execute_step(step_idx)
                self.step_results.append(step_result)
                
                # Save step to memory if it has useful information
                if step_result.get("reasoning"):
                    self.memory.add_memory(
                        self.task_id,
                        f"Step {step_idx} reasoning",
                        step_result["reasoning"]
                    )
                
                # Check if task is complete
                if is_done:
                    break
            
            # Generate task summary
            summary = self._generate_task_summary()
            
            # Record task completion
            self._record_event("task_execution_complete", {
                "task_id": self.task_id,
                "steps_taken": self.steps_taken,
                "is_complete": is_done,
                "duration": time.time() - task_start_time,
                "summary": summary,
            })
            
            # Save final trajectory
            self._save_trajectory()
            
            # Display summary
            if self.console:
                self.console.display_summary(self.steps_taken, summary)
            
            return {
                "task_id": self.task_id,
                "task": self.task,
                "steps_taken": self.steps_taken,
                "is_complete": is_done,
                "trajectory": self.trajectory,
                "summary": summary,
            }
        
        except Exception as e:
            error_msg = f"Task execution error: {str(e)}"
            
            # Log error
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            # Display error
            if self.console:
                self.console.print_error(error_msg)
            
            # Record task error
            self._record_event("task_execution_error", {
                "task_id": self.task_id,
                "error": error_msg,
                "traceback": traceback.format_exc(),
            })
            
            # Save trajectory even on error
            self._save_trajectory()
            
            return {
                "task_id": self.task_id,
                "task": self.task,
                "steps_taken": self.steps_taken,
                "error": error_msg,
                "is_complete": False,
                "trajectory": self.trajectory,
            }
    
    def _build_prompt(self, step_idx: int, context: Dict[str, Any]) -> str:
        """Build the prompt for the current step.
        
        Args:
            step_idx: Current step index.
            context: Context for this step.
            
        Returns:
            Formatted prompt.
        """
        # Basic prompt structure
        prompt = {
            "task": self.task,
            "step": step_idx,
            "context": context,
            "previous_steps": self.step_results,
        }
        
        # Include task arguments
        if self.task_args:
            prompt["task_args"] = self.task_args
        
        # Format prompt for the LLM client
        return self.llm_client.format_prompt(prompt)
    
    def _parse_response(self, response: str) -> Tuple[str, Optional[str], Optional[Dict], bool]:
        """Parse the LLM response to extract reasoning, action, and completion status.
        
        Args:
            response: Raw LLM response.
            
        Returns:
            Tuple of (reasoning, action, action_input, is_done)
        """
        # Default values
        reasoning = response
        action = None
        action_input = None
        is_done = False
        
        # Extract action block if present
        action_match = re.search(
            r"```(?:json)?(?:\s+)?(?:action|Action)(?:\s+)?(.+?)```",
            response,
            re.DOTALL,
        )
        
        if action_match:
            try:
                action_json = action_match.group(1).strip()
                action_data = json.loads(action_json)
                
                # Extract action and input
                action = action_data.get("name")
                action_input = action_data.get("input", {})
                
                # Remove action block from reasoning
                reasoning = re.sub(
                    r"```(?:json)?(?:\s+)?(?:action|Action)(?:\s+)?.+?```",
                    "",
                    reasoning,
                    flags=re.DOTALL,
                ).strip()
                
            except json.JSONDecodeError:
                self.logger.warning(f"Failed to parse action JSON: {action_match.group(1)}")
        
        # Check for task completion
        is_done = any(
            marker in response.lower()
            for marker in ["task complete", "task finished", "task is complete", "task is finished"]
        )
        
        return reasoning, action, action_input, is_done
    
    async def _execute_action(self, action: str, action_input: Dict) -> Dict:
        """Execute an action using the tool executor.
        
        Args:
            action: Action name.
            action_input: Action parameters.
            
        Returns:
            Action execution results.
        """
        try:
            # Record action execution attempt
            self._record_event("action_execution", {
                "task_id": self.task_id,
                "action": action,
                "action_input": action_input,
            })
            
            # Execute the tool
            result = await self.tool_executor.execute_tool(action, action_input)
            
            # Record successful execution
            self._record_event("action_execution_complete", {
                "task_id": self.task_id,
                "action": action,
                "success": True,
            })
            
            return result
        
        except Exception as e:
            error_msg = f"Action execution error ({action}): {str(e)}"
            
            # Log error
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            # Record action failure
            self._record_event("action_execution_error", {
                "task_id": self.task_id,
                "action": action,
                "error": error_msg,
            })
            
            return {
                "error": error_msg,
                "traceback": traceback.format_exc(),
            }
    
    def _generate_task_summary(self) -> str:
        """Generate a summary of the task execution.
        
        Returns:
            Task summary.
        """
        try:
            # For completed tasks, generate a summary using the LLM
            if len(self.step_results) > 0:
                summary_prompt = {
                    "task": self.task,
                    "steps": [
                        {
                            "step": i,
                            "reasoning": step.get("reasoning", ""),
                            "action": step.get("action", ""),
                            "is_done": step.get("is_done", False),
                        }
                        for i, step in enumerate(self.step_results)
                    ],
                }
                
                # Use asyncio.run here since we're within a synchronous method
                # but need to call an async method
                loop = asyncio.new_event_loop()
                summary = loop.run_until_complete(
                    self.llm_client.generate_summary(summary_prompt)
                )
                loop.close()
                
                return summary
            
            return "No steps were executed for this task."
        
        except Exception as e:
            self.logger.error(f"Error generating summary: {e}")
            return f"Error generating summary: {e}"
