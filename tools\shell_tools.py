"""Shell command tools for CLI Agent."""

import asyncio
import os
import shlex
import subprocess
from typing import Any, Dict, Optional

from .base import Tool


class RunShellCommandTool(Tool):
    """Tool for executing shell commands."""
    
    @property
    def name(self) -> str:
        return "run_shell"
    
    @property
    def description(self) -> str:
        return "Run a shell command and return its output."
    
    async def execute(
        self,
        command: str,
        cwd: Optional[str] = None,
        timeout: int = 30,
        env: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Execute a shell command.
        
        Args:
            command: The command to execute.
            cwd: Working directory for the command.
            timeout: Timeout in seconds.
            env: Additional environment variables.
            
        Returns:
            Dictionary with command output and metadata.
        """
        try:
            # Set up environment
            proc_env = os.environ.copy()
            if env:
                proc_env.update(env)
            
            # Use asyncio to execute the command with timeout
            proc = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd or os.getcwd(),
                env=proc_env,
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    proc.communicate(), timeout=timeout
                )
                return {
                    "command": command,
                    "exit_code": proc.returncode,
                    "stdout": stdout.decode("utf-8", errors="replace"),
                    "stderr": stderr.decode("utf-8", errors="replace"),
                    "success": proc.returncode == 0,
                    "timed_out": False,
                }
            except asyncio.TimeoutError:
                # Try to terminate the process
                proc.terminate()
                try:
                    await asyncio.wait_for(proc.wait(), timeout=2)
                except asyncio.TimeoutError:
                    proc.kill()
                
                return {
                    "command": command,
                    "exit_code": None,
                    "stdout": "",
                    "stderr": "Command execution timed out",
                    "success": False,
                    "timed_out": True,
                }
        
        except Exception as e:
            return {
                "command": command,
                "exit_code": -1,
                "stdout": "",
                "stderr": f"Error executing command: {str(e)}",
                "success": False,
                "timed_out": False,
            }
