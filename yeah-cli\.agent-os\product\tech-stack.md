# Technical Stack

> Last Updated: 2024-12-19
> Version: 1.0.0

## Core Technologies

### Application Framework
- **Language:** Python 3.9+
- **CLI Framework:** Click 8.0+
- **Package Management:** uv, setuptools

### File Processing
- **Markdown Processing:** python-frontmatter, markdown
- **YAML Processing:** PyYAML
- **File Operations:** pathlib (built-in)

### LLM Integration
- **OpenAI:** openai>=1.0.0
- **Anthropic:** anthropic>=0.8.0
- **Google AI:** google-generativeai
- **HTTP Client:** httpx

### Development Tools
- **Testing:** pytest, pytest-cov
- **Code Quality:** black, flake8, mypy
- **Documentation:** sphinx (for API docs)

## Architecture Decisions

### CLI Framework Choice: Click
- Excellent command structure support
- Built-in help generation
- Easy parameter validation
- Extensible command groups

### Multi-LLM Strategy
- Provider-agnostic interface
- Configurable provider selection
- Fallback provider support
- Rate limiting and error handling

### File-Based Configuration
- Follows Agent OS patterns
- Human-readable configuration
- Version controllable settings
- Easy to debug and modify

## Deployment Strategy

### Distribution
- **Primary:** PyPI package installation
- **Alternative:** Direct GitHub installation
- **Development:** pip install -e .

### Configuration
- **Global Config:** ~/.yeah-cli/config.yaml
- **Project Config:** .yeah-cli/config.yaml
- **Environment Variables:** YEAH_CLI_* prefix

### Installation Requirements
- Python 3.9 or higher
- Agent OS base installation
- LLM API credentials (user-provided)